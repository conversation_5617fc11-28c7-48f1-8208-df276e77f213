/node_modules
/public/hot
/public/storage
/storage/*.key
/vendor
.env
.env.backup
.env.build
/.phpunit.cache
.phpunit.result.cache
.phpunit.cache
Homestead.json
Homestead.yaml
npm-debug.log
yarn-error.log
package-lock.json

# Generated by MacOS
.DS_Store

# Generated by Windows
Thumbs.db

# Applications
*.app
*.exe
*.war

/.local
/.config
/.cache
/.idea
/.docker/data

/corretor-cury.local-key.pem
/corretor-cury.local.pem

/public/info.php

.env.develop
.env.master

/storage

.~lock*
./nginx
./nginx-logs
nginx-logs
./nginx-logs/access.log
./nginx-logs/error.log
.cursor

/2025-03_docker_old/.docker/data
/2025-03_docker_old/nginx
/2025-03_docker_old/nginx-logs

/_ide_helper.php
/.phpstorm.meta.php
debug-test.php
.vscode/
app/Http/Controllers/DebugTestController.php
docker/php/xdebug.ini

/public/local-images-tests
