<?php

declare(strict_types=1);

namespace App\Enums\BankAccount;

use App\Traits\EnumFunctions;

enum TfaTypeEnum: string
{
    use EnumFunctions;

    case SMS = 'sms';
    case EMAIL = 'email';
    case DEVICE = 'device';

    public function getLabel(): string
    {
        return match ($this) {
            self::SMS => 'SMS',
            self::EMAIL => 'E-mail',
            self::DEVICE => 'Dispositivo',
        };
    }

    public function getDescription(): string
    {
        return match ($this) {
            self::SMS => 'Autenticação via SMS',
            self::EMAIL => 'Autenticação via e-mail',
            self::DEVICE => 'Autenticação via dispositivo',
        };
    }

    public function requiresToken(): bool
    {
        return match ($this) {
            self::SMS, self::EMAIL => true,
            self::DEVICE => false,
        };
    }
}
