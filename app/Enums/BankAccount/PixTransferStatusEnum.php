<?php

declare(strict_types=1);

namespace App\Enums\BankAccount;

use App\Traits\EnumFunctions;

enum PixTransferStatusEnum: string
{
    use EnumFunctions;

    case PENDING = 'pending';
    case PROCESSING = 'processing';
    case COMPLETED = 'completed';
    case FAILED = 'failed';
    case CANCELLED = 'cancelled';

    public function getLabel(): string
    {
        return match ($this) {
            self::PENDING => 'Pendente',
            self::PROCESSING => 'Processando',
            self::COMPLETED => 'Concluída',
            self::FAILED => 'Falhou',
            self::CANCELLED => 'Cancelada',
        };
    }

    public function getDescription(): string
    {
        return match ($this) {
            self::PENDING => 'Transação criada, aguardando processamento',
            self::PROCESSING => 'Transação sendo processada',
            self::COMPLETED => 'Transação concluída com sucesso',
            self::FAILED => 'Transação falhou durante o processamento',
            self::CANCELLED => 'Transação foi cancelada',
        };
    }
}
