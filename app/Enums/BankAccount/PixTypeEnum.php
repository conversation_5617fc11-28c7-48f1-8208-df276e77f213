<?php

declare(strict_types=1);

namespace App\Enums\BankAccount;

use App\Traits\EnumFunctions;

enum PixTypeEnum: string
{
    use EnumFunctions;

    case MANUAL = 'manual';
    case QR_CODE = 'qr_code';
    case PIX_KEY = 'pix_key';

    public function getLabel(): string
    {
        return match ($this) {
            self::MANUAL => 'Manual',
            self::QR_CODE => 'QR Code',
            self::PIX_KEY => 'Chave PIX',
        };
    }

    public function getDescription(): string
    {
        return match ($this) {
            self::MANUAL => 'Transferência PIX manual com dados bancários',
            self::QR_CODE => 'Transferência PIX via QR Code',
            self::PIX_KEY => 'Transferência PIX via chave PIX',
        };
    }
}
