<?php

declare(strict_types=1);

namespace App\Http\Resources\BankAccount\PixTransfer;

use App\Services\BankAccount\FinancialInstitutionService;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ManualPixTransferResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $recipientData = $this->recipient_data;
        $financialInstitutionService = app(FinancialInstitutionService::class);
        $banks = $financialInstitutionService->listBanks();
        
        // Find bank name by ISPB
        $bankName = 'Banco não identificado';
        foreach ($banks as $bank) {
            if ($bank['ispb'] === $recipientData['ispb']) {
                $bankName = $bank['name'];
                break;
            }
        }

        return [
            'request_control_key' => $this->request_control_key,
            'pix_type' => $this->pix_type->value,
            'name' => $recipientData['name'],
            'document_number' => $this->formatDocumentNumber($recipientData['document_number']),
            'date' => $this->created_at->format('Y-m-d H:i:s'),
            'account' => [
                'ispb' => $recipientData['ispb'],
                'bank_name' => $bankName,
                'account_branch' => $recipientData['account_branch'],
                'account_number' => $recipientData['account_number'],
                'account_digit' => $recipientData['account_digit'],
                'account_type' => $recipientData['account_type'],
            ],
            'favorite' => [
                'is_favorite' => false,
                'name' => 'Exemplo, vai ser implementado depois',
            ],
        ];
    }

    /**
     * Format document number with mask
     */
    private function formatDocumentNumber(string $documentNumber): string
    {
        $cleanDocument = preg_replace('/[^0-9]/', '', $documentNumber);
        
        if (strlen($cleanDocument) === 11) {
            // CPF format: 000.000.000-00
            return preg_replace('/(\d{3})(\d{3})(\d{3})(\d{2})/', '$1.$2.$3-$4', $cleanDocument);
        } elseif (strlen($cleanDocument) === 14) {
            // CNPJ format: 00.000.000/0000-00
            return preg_replace('/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/', '$1.$2.$3/$4-$5', $cleanDocument);
        }
        
        return $documentNumber;
    }
}
