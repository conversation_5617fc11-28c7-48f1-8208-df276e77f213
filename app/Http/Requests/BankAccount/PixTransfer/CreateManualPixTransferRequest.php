<?php

declare(strict_types=1);

namespace App\Http\Requests\BankAccount\PixTransfer;

use App\Enums\BankAccount\AccountTypeEnum;
use App\Rules\Cpf;
use App\Rules\Cnpj;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateManualPixTransferRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'ispb' => ['required', 'string', 'size:8'],
            'account_branch' => ['required', 'string', 'max:10'],
            'account_number' => ['required', 'string', 'max:20'],
            'account_digit' => ['required', 'string', 'max:2'],
            'account_type' => ['required', 'string', Rule::in(AccountTypeEnum::getValidTypes())],
            'name' => ['required', 'string', 'max:100'],
            'document_number' => ['required', 'string', function ($attribute, $value, $fail) {
                $cleanDocument = preg_replace('/[^0-9]/', '', $value);
                
                if (strlen($cleanDocument) === 11) {
                    // CPF validation
                    $cpfRule = new Cpf();
                    if (!$cpfRule->passes($attribute, $value)) {
                        $fail('O CPF informado é inválido.');
                    }
                } elseif (strlen($cleanDocument) === 14) {
                    // CNPJ validation
                    $cnpjRule = new Cnpj();
                    if (!$cnpjRule->passes($attribute, $value)) {
                        $fail('O CNPJ informado é inválido.');
                    }
                } else {
                    $fail('O documento deve ser um CPF (11 dígitos) ou CNPJ (14 dígitos) válido.');
                }
            }],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'ispb.required' => 'O código ISPB é obrigatório.',
            'ispb.size' => 'O código ISPB deve ter exatamente 8 dígitos.',
            'account_branch.required' => 'A agência é obrigatória.',
            'account_branch.max' => 'A agência deve ter no máximo 10 caracteres.',
            'account_number.required' => 'O número da conta é obrigatório.',
            'account_number.max' => 'O número da conta deve ter no máximo 20 caracteres.',
            'account_digit.required' => 'O dígito da conta é obrigatório.',
            'account_digit.max' => 'O dígito da conta deve ter no máximo 2 caracteres.',
            'account_type.required' => 'O tipo de conta é obrigatório.',
            'account_type.in' => 'O tipo de conta deve ser um dos valores válidos.',
            'name.required' => 'O nome do destinatário é obrigatório.',
            'name.max' => 'O nome do destinatário deve ter no máximo 100 caracteres.',
            'document_number.required' => 'O documento do destinatário é obrigatório.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'ispb' => preg_replace('/[^0-9]/', '', $this->input('ispb', '')),
            'account_branch' => trim($this->input('account_branch', '')),
            'account_number' => preg_replace('/[^0-9]/', '', $this->input('account_number', '')),
            'account_digit' => preg_replace('/[^0-9]/', '', $this->input('account_digit', '')),
            'name' => trim($this->input('name', '')),
            'document_number' => preg_replace('/[^0-9]/', '', $this->input('document_number', '')),
        ]);
    }
}
