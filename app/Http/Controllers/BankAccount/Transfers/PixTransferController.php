<?php

declare(strict_types=1);

namespace App\Http\Controllers\BankAccount\Transfers;

use App\Enums\BankAccount\TfaTypeEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\BankAccount\PixTransfer\CreateManualPixTransferRequest;
use App\Http\Resources\BankAccount\PixTransfer\ManualPixTransferResource;
use App\Models\BankAccount;
use App\Services\BankAccount\PixTransferService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;

class PixTransferController extends Controller
{
    public function __construct(
        private readonly PixTransferService $pixTransferService
    ) {
    }

    /**
     * Create a new manual PIX transfer
     */
    public function createManual(CreateManualPixTransferRequest $request): JsonResponse
    {
        try {
            /** @var BankAccount $bankAccount */
            $bankAccount = $request->bankAccount;

            $recipientData = [
                'ispb' => $request->validated('ispb'),
                'account_branch' => $request->validated('account_branch'),
                'account_number' => $request->validated('account_number'),
                'account_digit' => $request->validated('account_digit'),
                'account_type' => $request->validated('account_type'),
                'name' => $request->validated('name'),
                'document_number' => $request->validated('document_number'),
            ];

            $pixTransfer = $this->pixTransferService->createManualPixTransfer($bankAccount, $recipientData);

            return response()->json(new ManualPixTransferResource($pixTransfer));

        } catch (\Exception $e) {
            Log::error('[PIX-TRANSFER] Error creating manual PIX transfer', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'error' => 'Erro interno do servidor. Tente novamente mais tarde.',
            ], 500);
        }
    }

    /**
     * Process PIX transfer with amount and password
     */
    public function processTransfer(Request $request, string $requestControlKey): JsonResponse
    {
        try {
            $validated = $request->validate([
                'amount' => ['required', 'numeric', 'min:0.01', 'max:999999.99'],
                'pix_message' => ['nullable', 'string', 'max:140'],
                'latitude' => ['required', 'numeric', 'min:-90', 'max:90'],
                'longitude' => ['required', 'numeric', 'min:-180', 'max:180'],
                'password' => ['required', 'string'],
                'tfa_type' => ['required', 'string', Rule::in(TfaTypeEnum::values())],
            ]);

            $pixTransfer = $this->pixTransferService->findByRequestControlKey($requestControlKey);

            if (!$pixTransfer) {
                return response()->json([
                    'error' => 'Transferência PIX não encontrada.',
                ], 404);
            }

            // Validate user password
            $user = $pixTransfer->bankAccount->user;
            if (!Hash::check($validated['password'], $user->password)) {
                return response()->json([
                    'error' => 'Senha inválida.',
                ], 401);
            }

            $this->pixTransferService->processPixTransfer(
                $pixTransfer,
                (float) $validated['amount'],
                $validated['pix_message'] ?? '',
                (float) $validated['latitude'],
                (float) $validated['longitude'],
                $validated['password'],
                TfaTypeEnum::from($validated['tfa_type'])
            );

            return response()->json([], 200);

        } catch (\Exception $e) {
            Log::error('[PIX-TRANSFER] Error processing PIX transfer', [
                'request_control_key' => $requestControlKey,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'error' => 'Erro interno do servidor. Tente novamente mais tarde.',
            ], 500);
        }
    }

    /**
     * Confirm PIX transfer with optional TFA token
     */
    public function confirmTransfer(Request $request, string $requestControlKey): JsonResponse
    {
        try {
            $pixTransfer = $this->pixTransferService->findByRequestControlKey($requestControlKey);

            if (!$pixTransfer) {
                return response()->json([
                    'error' => 'Transferência PIX não encontrada.',
                ], 404);
            }

            $tfaToken = null;

            // Validate TFA token if required
            if ($pixTransfer->requiresTfaToken()) {
                $validated = $request->validate([
                    'token' => ['required', 'string'],
                ]);
                $tfaToken = $validated['token'];
            }

            $this->pixTransferService->confirmPixTransfer($pixTransfer, $tfaToken);

            return response()->json([], 200);

        } catch (\InvalidArgumentException $e) {
            return response()->json([
                'error' => $e->getMessage(),
            ], 400);
        } catch (\Exception $e) {
            Log::error('[PIX-TRANSFER] Error confirming PIX transfer', [
                'request_control_key' => $requestControlKey,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'error' => 'Erro interno do servidor. Tente novamente mais tarde.',
            ], 500);
        }
    }

    /**
     * Get PIX transfer status
     */
    public function getStatus(string $requestControlKey): JsonResponse
    {
        try {
            $pixTransfer = $this->pixTransferService->findByRequestControlKey($requestControlKey);

            if (!$pixTransfer) {
                return response()->json([
                    'error' => 'Transferência PIX não encontrada.',
                ], 404);
            }

            $statusData = $this->pixTransferService->getTransferStatus($pixTransfer);

            return response()->json($statusData);

        } catch (\Exception $e) {
            Log::error('[PIX-TRANSFER] Error getting PIX transfer status', [
                'request_control_key' => $requestControlKey,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'error' => 'Erro interno do servidor. Tente novamente mais tarde.',
            ], 500);
        }
    }

    /**
     * Get PIX transfer receipt (placeholder)
     */
    public function getReceipt(string $requestControlKey): JsonResponse
    {
        // TODO: Implement receipt generation
        return response()->json([
            'message' => 'Comprovante será implementado em uma próxima versão.',
        ]);
    }

    /**
     * Add PIX transfer to favorites (placeholder)
     */
    public function addToFavorites(Request $request, string $requestControlKey): JsonResponse
    {
        try {
            $validated = $request->validate([
                'name' => ['required', 'string', 'max:100'],
            ]);

            // TODO: Implement favorite functionality
            return response()->json([], 200);

        } catch (\Exception $e) {
            Log::error('[PIX-TRANSFER] Error adding PIX transfer to favorites', [
                'request_control_key' => $requestControlKey,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'error' => 'Erro interno do servidor. Tente novamente mais tarde.',
            ], 500);
        }
    }
}
