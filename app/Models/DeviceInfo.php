<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use MongoDB\Laravel\Relations\BelongsTo;

class DeviceInfo extends Model
{
    use HasFactory;

    protected $table = 'device_infos';

    protected $fillable = [
        'pix_transfer_id',
        'base_os',
        'system_version',
        'brand',
        'device',
        'device_id',
        'unique_id',
        'readable_version',
        'is_airplane_mode',
        'is_emulator',
        'is_location_enabled',
        'ip_address',
    ];

    protected $casts = [
        'is_airplane_mode' => 'boolean',
        'is_emulator' => 'boolean',
        'is_location_enabled' => 'boolean',
    ];

    public function pixTransfer(): BelongsTo
    {
        return $this->belongsTo(PixTransfer::class);
    }

    public static function createFromHeaders(PixTransfer $pixTransfer): ?self
    {
        $headers = getAppCuryHeaders();
        
        if (empty($headers)) {
            return null;
        }

        return self::create([
            'pix_transfer_id' => $pixTransfer->id,
            'base_os' => $headers['base-os'] ?? null,
            'system_version' => $headers['system-version'] ?? null,
            'brand' => $headers['brand'] ?? null,
            'device' => $headers['device'] ?? null,
            'device_id' => $headers['device-id'] ?? null,
            'unique_id' => $headers['unique-id'] ?? null,
            'readable_version' => $headers['readable-version'] ?? null,
            'is_airplane_mode' => $headers['is-airplane-mode'] === 'true',
            'is_emulator' => $headers['is-emulator'] === 'true',
            'is_location_enabled' => $headers['is-location-enabled'] === 'true',
            'ip_address' => $headers['ip'] ?? null,
        ]);
    }
}
