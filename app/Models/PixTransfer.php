<?php

declare(strict_types=1);

namespace App\Models;

use App\Enums\BankAccount\PixTransferStatusEnum;
use App\Enums\BankAccount\PixTypeEnum;
use App\Enums\BankAccount\TfaTypeEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use MongoDB\Laravel\Relations\BelongsTo;

class PixTransfer extends Model
{
    use HasFactory;

    protected $table = 'pix_transfers';

    protected $casts = [
        'status' => PixTransferStatusEnum::class,
        'pix_type' => PixTypeEnum::class,
        'tfa_type' => TfaTypeEnum::class,
        'amount' => 'float',
        'latitude' => 'float',
        'longitude' => 'float',
        'recipient_data' => 'array',
        'device_infos' => 'array',
        'completed_at' => 'datetime',
        'failed_at' => 'datetime',
        'cancelled_at' => 'datetime',
        'confirmed_at' => 'datetime',
        'is_favorite' => 'boolean',
    ];

    protected $fillable = [
        'bank_account_id',
        'request_control_key',
        'status',
        'pix_type',
        'recipient_data',
        'amount',
        'pix_message',
        'latitude',
        'longitude',
        'password_hash',
        'tfa_type',
        'tfa_token',
        'device_infos',
        'completed_at',
        'failed_at',
        'cancelled_at',
        'confirmed_at',
        'error_message',
        'is_favorite',
        'favorite_name',
    ];

    protected $hidden = [
        'password_hash',
        'tfa_token',
    ];

    public function bankAccount(): BelongsTo
    {
        return $this->belongsTo(BankAccount::class);
    }

    public function generateRequestControlKey(): string
    {
        return $this->pub_id;
    }

    public function isPending(): bool
    {
        return $this->status === PixTransferStatusEnum::PENDING;
    }

    public function isProcessing(): bool
    {
        return $this->status === PixTransferStatusEnum::PROCESSING;
    }

    public function isCompleted(): bool
    {
        return $this->status === PixTransferStatusEnum::COMPLETED;
    }

    public function isFailed(): bool
    {
        return $this->status === PixTransferStatusEnum::FAILED;
    }

    public function isCancelled(): bool
    {
        return $this->status === PixTransferStatusEnum::CANCELLED;
    }

    public function canBeConfirmed(): bool
    {
        return $this->isPending() && !empty($this->password_hash);
    }

    public function requiresTfaToken(): bool
    {
        return $this->tfa_type && $this->tfa_type->requiresToken();
    }

    public function markAsCompleted(): void
    {
        $this->update([
            'status' => PixTransferStatusEnum::COMPLETED,
            'completed_at' => now(),
        ]);
    }

    public function markAsFailed(string $errorMessage = null): void
    {
        $this->update([
            'status' => PixTransferStatusEnum::FAILED,
            'failed_at' => now(),
            'error_message' => $errorMessage,
        ]);
    }

    public function markAsCancelled(): void
    {
        $this->update([
            'status' => PixTransferStatusEnum::CANCELLED,
            'cancelled_at' => now(),
        ]);
    }

    public function markAsProcessing(): void
    {
        $this->update([
            'status' => PixTransferStatusEnum::PROCESSING,
        ]);
    }

    public function markAsConfirmed(): void
    {
        $this->update([
            'confirmed_at' => now(),
        ]);
    }
}
