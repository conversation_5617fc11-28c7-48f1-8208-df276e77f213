<?php

namespace App\Models;

use App\Enums\BankAccount\DocumentTypeEnum;
use App\Enums\BankAccount\StatusEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use MongoDB\Laravel\Relations\HasMany;

class BankAccount extends Model
{
    use HasFactory;

    /**
     * The collection associated with the model.
     */
    protected $table = 'bank_accounts';

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'status' => StatusEnum::class,
        'is_active' => 'bool',
        'terms_baas_accepted_at' => 'datetime',
        'contract_cury_send_at' => 'datetime',
        'contract_cury_signed_at' => 'datetime',
        'account_confirmed_at' => 'datetime',
        'account_reserved_at' => 'datetime',
        'account_rejected_at' => 'datetime',
        'account_salesforce_saved_at' => 'datetime',
        'account_creation_notified_at' => 'datetime',
        'document_type' => DocumentTypeEnum::class
    ];

    /**
     * Obtém o modelo de usuário associado (UserApp ou UserGestor).
     *
     */
    public function user(): MorphTo
    {
        return $this->morphTo();
    }

    public function webhookLogs(): HasMany
    {
        return $this->hasMany(QiTechWebhookLog::class);
    }

    public function pixTransfers(): HasMany
    {
        return $this->hasMany(PixTransfer::class);
    }

    public function isActive(): bool
    {
        return $this->isConfirmed() && $this->user->createdOnSalesforce() && $this->user->isCuryContractSigned();
    }
    public function isConfirmed(): bool
    {
        return $this->status === StatusEnum::ACCOUNT_CONFIRMED;
    }
    public function isRejected(): bool
    {
        return $this->status === StatusEnum::CREATING_ACCOUNT_REPROVED;
    }

    /**
     * Verifica se os documentos de validação estão preenchidos
     */
    public function hasValidationDocuments(): bool
    {
        return !empty($this->validation_document_identification) && !empty($this->validation_proof_of_residence);
    }

    public function getNotificationStatus(): ?string
    {
        if ($this->account_creation_notified_at) {
            return null;
        }

        return match (true) {
            $this->isRejected() => 'error',
            $this->isActive() => 'created',
            !$this->isConfirmed() => 'pending_account',
            !$this->user->createdOnSalesforce() => 'pending_salesforce',
            !$this->user->isCuryContractSigned() => 'pending_contract',
            default => null,
        };
    }
}
