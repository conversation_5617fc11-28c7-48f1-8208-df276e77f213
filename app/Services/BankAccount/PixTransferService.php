<?php

declare(strict_types=1);

namespace App\Services\BankAccount;

use App\Enums\BankAccount\PixTransferStatusEnum;
use App\Enums\BankAccount\PixTypeEnum;
use App\Enums\BankAccount\TfaTypeEnum;
use App\Models\BankAccount;
use App\Models\DeviceInfo;
use App\Models\PixTransfer;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;

class PixTransferService
{
    /**
     * Create a new manual PIX transfer
     */
    public function createManualPixTransfer(BankAccount $bankAccount, array $recipientData): PixTransfer
    {
        $pixTransfer = PixTransfer::create([
            'bank_account_id' => $bankAccount->id,
            'request_control_key' => null, // Will be set to pub_id after creation
            'status' => PixTransferStatusEnum::PENDING,
            'pix_type' => PixTypeEnum::MANUAL,
            'recipient_data' => $recipientData,
        ]);

        // Set request_control_key to pub_id
        $pixTransfer->update([
            'request_control_key' => $pixTransfer->pub_id,
        ]);

        Log::info('[PIX-TRANSFER] Manual PIX transfer created', [
            'pix_transfer_id' => $pixTransfer->id,
            'bank_account_id' => $bankAccount->id,
            'request_control_key' => $pixTransfer->request_control_key,
        ]);

        return $pixTransfer;
    }

    /**
     * Process PIX transfer with amount and password
     */
    public function processPixTransfer(
        PixTransfer $pixTransfer,
        float $amount,
        string $pixMessage,
        float $latitude,
        float $longitude,
        string $password,
        TfaTypeEnum $tfaType
    ): void {
        if (!$pixTransfer->canBeConfirmed()) {
            throw new \InvalidArgumentException('PIX transfer cannot be processed in current state');
        }

        // Store device information
        DeviceInfo::createFromHeaders($pixTransfer);

        // Update transfer with transaction data
        $pixTransfer->update([
            'amount' => $amount,
            'pix_message' => $pixMessage,
            'latitude' => $latitude,
            'longitude' => $longitude,
            'password_hash' => Hash::make($password),
            'tfa_type' => $tfaType,
        ]);

        Log::info('[PIX-TRANSFER] PIX transfer processed', [
            'pix_transfer_id' => $pixTransfer->id,
            'request_control_key' => $pixTransfer->request_control_key,
            'amount' => $amount,
            'tfa_type' => $tfaType->value,
        ]);
    }

    /**
     * Confirm PIX transfer with optional TFA token
     */
    public function confirmPixTransfer(PixTransfer $pixTransfer, ?string $tfaToken = null): void
    {
        if (!$pixTransfer->canBeConfirmed()) {
            throw new \InvalidArgumentException('PIX transfer cannot be confirmed in current state');
        }

        // Validate TFA token if required
        if ($pixTransfer->requiresTfaToken()) {
            if (empty($tfaToken)) {
                throw new \InvalidArgumentException('TFA token is required for this transfer');
            }
            // TODO: Implement TFA token validation logic
        }

        $pixTransfer->markAsConfirmed();
        $pixTransfer->markAsProcessing();

        Log::info('[PIX-TRANSFER] PIX transfer confirmed', [
            'pix_transfer_id' => $pixTransfer->id,
            'request_control_key' => $pixTransfer->request_control_key,
            'tfa_type' => $pixTransfer->tfa_type?->value,
        ]);

        // TODO: Here we would integrate with the actual PIX API
        // For now, we'll simulate success
        $this->simulatePixTransferExecution($pixTransfer);
    }

    /**
     * Find PIX transfer by request control key
     */
    public function findByRequestControlKey(string $requestControlKey): ?PixTransfer
    {
        return PixTransfer::where('request_control_key', $requestControlKey)->first();
    }

    /**
     * Get PIX transfer status information
     */
    public function getTransferStatus(PixTransfer $pixTransfer): array
    {
        $recipientData = $pixTransfer->recipient_data;
        
        return [
            'status' => $pixTransfer->status->value,
            'recipient' => [
                'name' => $recipientData['name'] ?? null,
                'amount' => $pixTransfer->amount,
                'date' => $pixTransfer->completed_at?->format('Y-m-d H:i:s'),
            ],
            'message' => $pixTransfer->error_message ?? '',
        ];
    }

    /**
     * Simulate PIX transfer execution (temporary implementation)
     */
    private function simulatePixTransferExecution(PixTransfer $pixTransfer): void
    {
        // Simulate processing delay and success
        // In a real implementation, this would call the actual PIX API
        $pixTransfer->markAsCompleted();

        Log::info('[PIX-TRANSFER] PIX transfer completed (simulated)', [
            'pix_transfer_id' => $pixTransfer->id,
            'request_control_key' => $pixTransfer->request_control_key,
        ]);
    }
}
