# PIX Transfers - Transferências Manuais

Este documento descreve a implementação do sistema de transferências PIX manuais (com agência e conta).

## Endpoints Implementados

### 1. POST /transfers/pix/manual
**Descrição:** Endpoint inicial para criar uma transferência PIX manual após o usuário preencher os dados da conta.

**Payload:**
```json
{
    "ispb": "********",
    "account_branch": "1234",
    "account_number": "*********",
    "account_digit": "0",
    "account_type": "checking_account",
    "name": "<PERSON>",
    "document_number": "*********01"
}
```

**Response:**
```json
{
    "request_control_key": "uuid-gerado",
    "pix_type": "manual",
    "name": "<PERSON>",
    "document_number": "123.456.789-01",
    "date": "2025-01-20 10:30:00",
    "account": {
        "ispb": "********",
        "bank_name": "Nome do Banco",
        "account_branch": "1234",
        "account_number": "*********",
        "account_digit": "0",
        "account_type": "checking_account"
    },
    "favorite": {
        "is_favorite": false,
        "name": "Exemplo, vai ser implementado depois"
    }
}
```

### 2. POST /transfers/pix/{request_control_key}
**Descrição:** Realiza a transação PIX com validação de senha e captura de informações do dispositivo.

**Headers Necessários:**
- `appcury-base-os`
- `appcury-system-version`
- `appcury-brand`
- `appcury-device`
- `appcury-device-id`
- `appcury-unique-id`
- `appcury-readable-version`
- `appcury-is-airplane-mode`
- `appcury-is-emulator`
- `appcury-is-location-enabled`

**Payload:**
```json
{
    "amount": 100.50,
    "pix_message": "Mensagem do PIX (máx 140 chars)",
    "latitude": -23.5505,
    "longitude": -46.6333,
    "password": "senha_do_usuario",
    "tfa_type": "sms|email|device"
}
```

**Response:** Status 200 com body vazio

### 3. POST /transfers/pix/{request_control_key}/confirm
**Descrição:** Confirma a transação PIX. Pode ou não receber um token dependendo do tipo de 2FA.

**Payload (quando tfa_type é 'sms' ou 'email'):**
```json
{
    "token": "123456"
}
```

**Payload (quando tfa_type é 'device'):**
```json
{}
```

**Response:** Status 200 com body vazio

### 4. GET /transfers/pix/{request_control_key}/status
**Descrição:** Consulta o status da transação PIX.

**Response:**
```json
{
    "status": "completed|pending|processing|failed|cancelled",
    "recipient": {
        "name": "João da Silva",
        "amount": 100.50,
        "date": "2025-01-20 10:35:00"
    },
    "message": "Mensagem de erro (vazio em caso de sucesso)"
}
```

### 5. GET /transfers/pix/{request_control_key}/receipt
**Descrição:** Retorna o comprovante da transação (placeholder).

**Response:**
```json
{
    "message": "Comprovante será implementado em uma próxima versão."
}
```

### 6. POST /transfers/pix/{request_control_key}/add-favorite
**Descrição:** Adiciona a transferência aos favoritos (placeholder).

**Payload:**
```json
{
    "name": "Nome para salvar o favorito"
}
```

**Response:** Status 200 com body vazio

## Estruturas de Dados

### Enums Criados
- `PixTransferStatusEnum`: pending, processing, completed, failed, cancelled
- `PixTypeEnum`: manual, qr_code, pix_key
- `TfaTypeEnum`: sms, email, device

### Models Criados
- `PixTransfer`: Armazena as transferências PIX
- `DeviceInfo`: Armazena informações do dispositivo

### Tabelas Criadas
- `pix_transfers`: Transferências PIX
- `device_infos`: Informações dos dispositivos

## Validações Implementadas

### Validação de Documento
- CPF: 11 dígitos com validação usando a regra `Cpf`
- CNPJ: 14 dígitos com validação usando a regra `Cnpj`

### Validação de Conta
- ISPB: Exatamente 8 dígitos
- Agência: Máximo 10 caracteres
- Conta: Máximo 20 dígitos
- Dígito: Máximo 2 caracteres
- Tipo de conta: Deve ser um dos valores válidos do `AccountTypeEnum`

### Validação de Senha
- Validação da senha do usuário usando `Hash::check()`

### Validação de 2FA
- Para `sms` e `email`: Token obrigatório
- Para `device`: Token não necessário

## Tratamento de Erros

Todos os endpoints retornam erros padronizados:

- **400 Bad Request**: Dados inválidos ou estado inválido da transação
- **401 Unauthorized**: Senha inválida
- **404 Not Found**: Transferência PIX não encontrada
- **422 Unprocessable Entity**: Erros de validação
- **500 Internal Server Error**: Erros internos do servidor

## Testes

### Testes de Feature
- `tests/Feature/BankAccount/PixTransferTest.php`

### Testes Unitários
- `tests/Unit/Services/PixTransferServiceTest.php`

### Factory
- `database/factories/PixTransferFactory.php`

## Próximos Passos

1. **Integração com API PIX Real**: Substituir a simulação por chamadas reais à API do provedor PIX
2. **Implementação de Webhooks**: Para atualizar status das transações automaticamente
3. **Sistema de Favoritos**: Implementar funcionalidade completa de favoritos
4. **Comprovantes**: Gerar comprovantes em PDF
5. **Notificações**: Enviar notificações push quando o status da transação mudar
6. **Validação de 2FA**: Implementar validação real de tokens SMS/email

## Middleware e Segurança

As rotas estão protegidas pelos middlewares:
- `has-bank-account:account_confirmed`: Verifica se o usuário tem conta bancária confirmada
- `has-active-bank-account`: Verifica se a conta bancária está ativa

## Logs

Todos os endpoints geram logs detalhados para auditoria e debugging:
- Criação de transferências
- Processamento de transações
- Confirmação de transferências
- Erros e exceções
