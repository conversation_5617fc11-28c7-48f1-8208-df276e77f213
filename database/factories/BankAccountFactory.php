<?php

namespace Database\Factories;

use App\Enums\BankAccount\DocumentTypeEnum;
use App\Enums\BankAccount\StatusEnum;
use App\Models\AuthJWTModel;
use App\Models\BankAccount;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\Drivers\Imagick\Driver;
use Intervention\Image\ImageManager;

class BankAccountFactory extends Factory
{

    protected $model = BankAccount::class;

    /**
     * Define os atributos padrão para a conta bancária fake gerada pela factory.
     * Utiliza o provider 'qi_tech' e status inicial.
     */
    public function definition(): array
    {
        return [
            'provider' => 'qi_tech',
            'user_id' => null,
            'user_type' => null,
            'status' => StatusEnum::INITIAL,
            'terms_baas_accepted_at' => null,
            'terms_baas_accepted_object' => null,

        ];
    }

    /**
     * Define o usuário dono da conta bancária, associando user_id e user_type.
     * Gera um novo account_key para cada usuário.
     */
    public function user(AuthJWTModel $user): self
    {
        return $this->state(fn() => [
            'user_id' => $user->id,
            'user_type' => get_class($user)
        ]);
    }

    /**
     * Marca que o usuário aceitou os termos do BaaS, preenchendo status e dados do aceite.
     */
    public function termsAccepted(): self
    {
        return $this->state(function () {

            $now = now();

            return [
                'status' => StatusEnum::TERMS_ACCEPTED,
                'signed_contract' => [
                    'document_key' => $this->faker->uuid(),
                ],
                'terms_baas_accepted_at' => $now,
                'terms_baas_accepted_object' => [
                    'timestamp' => $now->format('Y-m-d\TH:i:s.u\Z'),
                    'lat' => $this->faker->latitude(),
                    'long' => $this->faker->longitude(),
                    'ip_address' => $this->faker->ipv4(),
                ],
            ];
        });
    }

    /**
     * Gera dados fake de upload de documentos, variando conforme o tipo de documento informado.
     * Utilizado para simular uploads de documentos em testes.
     */
    private function makeDocumentUploadData(DocumentTypeEnum $documentType = DocumentTypeEnum::RG_FRONT_BACK): array
    {
        $now = now();
        $baseDocumentData = [
            'created_at' => $now,
            'origin_filename' => 'documento_original.jpg',
            'filename' => 'documento_original.jpg',
            'path' => 'documento_original.jpg',
            'thumb_filename' => 'thumb_documento_original.jpg',
            'thumb_path' => 'thumbs/thumb_documento_original.jpg',
            'type' => 'image',
        ];
        $baseDocumentDataFront = [
            'created_at' => $now,
            'origin_filename' => 'rg_front.jpeg',
            'filename' => 'rg_front.jpeg',
            'path' => 'rg_front.jpeg',
            'thumb_filename' => 'thumb_rg_front.jpeg',
            'thumb_path' => 'thumbs/thumb_rg_front.jpeg',
            'type' => 'image',
        ];
        $baseDocumentDataBack = [
            'created_at' => $now,
            'origin_filename' => 'rg_back.jpeg',
            'filename' => 'rg_back.jpeg',
            'path' => 'rg_back.jpeg',
            'thumb_filename' => 'thumb_rg_back.jpeg',
            'thumb_path' => 'thumbs/thumb_rg_back.jpeg',
            'type' => 'image',
        ];

        $proofOfResidenceDocumentData = [
            'created_at' => $now,
            'origin_filename' => 'comprovante-de-residencia.jpeg',
            'filename' => 'comprovante-de-residencia.jpeg',
            'path' => 'comprovante-de-residencia.jpeg',
            'thumb_filename' => 'thumb_comprovante-de-residencia.jpeg',
            'thumb_path' => 'thumbs/thumb_comprovante-de-residencia.jpeg',
            'type' => 'image',
        ];
        $selfieDocumentData = [
            'created_at' => $now,
            'origin_filename' => 'selfie.jpeg',
            'filename' => 'selfie.jpeg',
            'path' => 'selfie.jpeg',
            'thumb_filename' => 'thumb_selfie.jpeg',
            'thumb_path' => 'thumbs/thumb_selfie.jpeg',
            'type' => 'image',
        ];

        $pdfDocumentData = [
            'created_at' => $now,
            'origin_filename' => 'documento_original.pdf',
            'filename' => 'documento_original.pdf',
            'path' => 'documento_original.pdf',
            'type' => 'pdf',
        ];

        $data = [
            'document_upload_proof_of_residence' => $proofOfResidenceDocumentData,
            'document_upload_selfie' => $selfieDocumentData,
        ];

        match ($documentType) {
            DocumentTypeEnum::RG_FRONT_BACK =>
            $data += [
                'document_upload_rg_front' => $baseDocumentDataFront,
                'document_upload_rg_back' => $baseDocumentDataBack,
            ],
            DocumentTypeEnum::CNH_DIGITAL =>
            $data += [
                'document_upload_cnh_digital' => $pdfDocumentData,
            ],
            DocumentTypeEnum::CNH_SINGLE =>
            $data += [
                'document_upload_cnh_single' => $baseDocumentData,
            ],
            DocumentTypeEnum::CNH_FRONT_BACK =>
            $data += [
                'document_upload_cnh_front' => $baseDocumentData,
                'document_upload_cnh_back' => $baseDocumentData,
            ],
        };

        return $data;
    }

    /**
     * Adiciona dados de upload de documentos à factory, conforme o tipo de documento.
     * Permite simular diferentes cenários de envio de documentos, com a possibilidade de sobrescrever dados.
     */
    public function documentsUpload(DocumentTypeEnum $documentType = DocumentTypeEnum::RG_FRONT_BACK, array $overrides = []): self
    {
        return $this->state(function () use ($documentType, $overrides) {
            $data = $this->makeDocumentUploadData($documentType);

            return array_replace_recursive($data, $overrides);
        });
    }

    /**
     * Variante do state documentsUpload que cria arquivos reais no storage.
     * - Para campos 'type' => 'image', cria um PNG 1x1.
     * - Para campos 'type' => 'pdf', cria um PDF mínimo.
     *
    */
    public function documentsUploadRealFiles(
        DocumentTypeEnum $documentType = DocumentTypeEnum::RG_FRONT_BACK,
        array $overrides = [],
        string $disk = 'bank-account'
    ): self {
        return $this->state(function () use ($documentType, $overrides, $disk) {
            $data = $this->makeDocumentUploadData($documentType);

            $baseDir = 'tests-documents/' . Str::uuid();

            foreach ($data as $key => &$doc) {
                if (!is_array($doc) || !str_starts_with((string) $key, 'document_upload_')) {
                    continue;
                }

                // Mantém os nomes já definidos pela factory; se não houver, força .jpeg
                $originalName = $doc['filename'] ?? ($doc['origin_filename'] ?? 'image.jpeg');
                if (! str_ends_with(strtolower($originalName), '.jpeg') && ($doc['type'] ?? 'image') !== 'pdf') {
                    $originalName .= '.jpeg';
                }

                $thumbName = $doc['thumb_filename'] ?? ('thumb_' . pathinfo($originalName, PATHINFO_BASENAME));
                if (! str_ends_with(strtolower($thumbName), '.jpeg') && ($doc['type'] ?? 'image') !== 'pdf') {
                    $thumbName .= '.jpeg';
                }

                $docPath = $baseDir . '/' . $originalName;
                $thumbPath = $baseDir . '/thumbs/' . $thumbName;

                if (($doc['type'] ?? 'image') === 'pdf') {
                    $this->writeTinyPdf($disk, $docPath);
                } else {
                    // Gera JPEG pequeno e compatível com Intervention
                    $this->writeTinyJpeg($disk, $docPath);
                    $this->writeTinyJpeg($disk, $thumbPath);
                }

                // Atualiza metadados
                $doc['filename'] = $originalName;
                $doc['path'] = $docPath;
                $doc['origin_filename'] = $doc['origin_filename'] ?? $originalName;
                $doc['created_at'] = $doc['created_at'] ?? now();

                if (($doc['type'] ?? 'image') !== 'pdf') {
                    $doc['thumb_filename'] = $thumbName;
                    $doc['thumb_path'] = $thumbPath;
                    $doc['type'] = 'image';
                } else {
                    unset($doc['thumb_filename'], $doc['thumb_path']);
                    $doc['type'] = 'pdf';
                }
            }
            unset($doc);

            return array_replace_recursive($data, $overrides);
        });

    }

    private function writeTinyJpeg(string $disk, string $path): void
    {
        $manager = new ImageManager(new Driver());
        $image = $manager->create(50, 50)->fill('#ffffff');
        $jpeg = $image->toJpeg(quality: 85)->toString();

        Storage::disk($disk)->put($path, $jpeg);
    }

    private function writeTinyPdf(string $disk, string $path): void
    {
        $pdfContent = "%PDF-1.4\n1 0 obj\n<<>>\nendobj\ntrailer\n<<>>\n%%EOF\n";
        Storage::disk($disk)->put($path, $pdfContent);
    }



    /**
     * Adiciona dados de upload de documentos com informações de OCR (reconhecimento óptico de caracteres).
     * Simula o processamento de OCR em documentos enviados.
     */
    public function documentsOCR(DocumentTypeEnum $documentType = DocumentTypeEnum::RG_FRONT_BACK): self
    {
        return $this->state(function () use ($documentType) {

            $uploadState = $this->makeDocumentUploadData($documentType);

            $uploadState['document_upload_proof_of_residence']['ocr_key'] = $this->faker->uuid();
            $uploadState['document_upload_selfie']['recognition_key'] = [
                'image_key' => $this->faker->uuid(),
                'file_size' => 47407,
                'width_px' => 0,
                'height_px' => 0,
                'created_at' => now()->format('Y-m-d\TH:i:s\Z'),
            ];

            // Adiciona os documentos específicos com base no tipo
            switch ($documentType) {
                case DocumentTypeEnum::RG_FRONT_BACK:
                    $uploadState['document_upload_rg_front'] += ['ocr_key' => $this->faker->uuid()];
                    $uploadState['document_upload_rg_back'] += ['ocr_key' => $this->faker->uuid()];
                    break;

                case DocumentTypeEnum::CNH_DIGITAL:
                    $uploadState['document_upload_cnh_digital'] += ['ocr_key' => $this->faker->uuid()];
                    break;

                case DocumentTypeEnum::CNH_SINGLE:
                    $uploadState['document_upload_cnh_single'] += ['ocr_key' => $this->faker->uuid()];
                    break;

                case DocumentTypeEnum::CNH_FRONT_BACK:
                    $uploadState['document_upload_cnh_front'] += ['ocr_key' => $this->faker->uuid()];
                    $uploadState['document_upload_cnh_back'] += ['ocr_key' => $this->faker->uuid()];
                    break;
            }
            return array_merge(['document_type' => $documentType->value], $uploadState);
        });
    }

    /**
     * Define os dados pessoais e endereço informados pelo usuário para criação da conta.
     * Simula o preenchimento do formulário de abertura de conta.
     */
    public function userCreateAccountData(): self
    {
        return $this->state(fn(array $attributes) => [
            'user_create_account_data' => [

                'birthdate' => $this->faker->date('Y-m-d'),
                'mother_name' => $this->faker->name(),
                'is_pep' => $this->faker->boolean(10),
                'nationality' => 'Brasileiro',
                'monthly_income' => (float) number_format($this->faker->randomFloat(2, 2000, 15000), 2, '.', ''),
                'document_identification' => $this->faker->uuid(),
                'address' => [
                    'street' => $this->faker->streetName(),
                    'number' => $this->faker->buildingNumber(),
                    'complement' => $this->faker->optional(0.3)->secondaryAddress(),
                    'neighborhood' => $this->faker->citySuffix(),
                    'city' => $this->faker->city(),
                    'state' => $this->faker->stateAbbr(),
                    'postal_code' => $this->faker->numerify('#####-###')
                ]
            ]
        ]);
    }

    /**
     * Define o status da conta como "criando conta" (em andamento).
     */
    public function statusCreatingAccount(): self
    {
        return $this->state(fn() => [
            'status' => StatusEnum::CREATING_ACCOUNT
        ]);
    }

    /**
     * Define o status da conta como "criação reprovada".
     */
    public function statusCreatingAccountReproved(): self
    {
        return $this->state(fn() => [
            'status' => StatusEnum::CREATING_ACCOUNT_REPROVED,
            'account_rejected_at' => now(),
        ]);
    }

    /**
     * Define o status da conta como "em análise" e preenche dados de agência, conta e status do pedido.
     */
    public function statusCreatingAccountAnalysis(): self
    {
        return $this->state(fn() => [
            'status' => StatusEnum::CREATING_ACCOUNT_ANALYSIS,
            'account_info' => [
                'account_branch' => '0001',
                'account_digit' => '0',
                'account_number' => '1693580'
            ],
            'account_request_key' => $this->faker->uuid(),
            'account_request_status' => 'pending_kyc_analysis'
        ]);
    }

    /**
     * Define o status da conta como "conta criada" e preenche dados completos da conta bancária.
     */
    public function statusAccountCreated(): self
    {
        return $this->state(fn() => [
            'status' => StatusEnum::ACCOUNT_CREATED,
            'account_info' => [
                'account_key' => 'b7593804-2223-48b3-8a61-f48a651de1d4',
                'account_digit' => '5',
                'account_branch' => '0001',
                'account_number' => '3998360',
                'financial_institution_code' => '329'
            ],
            'account_request_key' => $this->faker->uuid(),
            'account_request_status' => 'account_opened',
            'account_reserved_at' => now(),
        ]);
    }

    /**
     * Define o status da conta como "conta confirmada" e preenche dados completos da conta bancária.
     */
    public function statusAccountConfirmed(): self
    {
        return $this->state(fn() => [
            'status' => StatusEnum::ACCOUNT_CONFIRMED,
            'account_info' => [
                'account_key' => 'b7593804-2223-48b3-8a61-f48a651de1d4',
                'account_digit' => '5',
                'account_branch' => '0001',
                'account_number' => '3998360',
                'financial_institution_code' => '329'
            ],
            'account_request_key' => $this->faker->uuid(),
            'account_request_status' => 'account_opened',
            'account_confirmed_at' => now(),
        ]);
    }
}
