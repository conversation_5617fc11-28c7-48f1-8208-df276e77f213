<?php

namespace Database\Factories;

use App\Enums\BankAccount\PixTransferStatusEnum;
use App\Enums\BankAccount\PixTypeEnum;
use App\Enums\BankAccount\TfaTypeEnum;
use App\Models\BankAccount;
use App\Models\PixTransfer;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;

class PixTransferFactory extends Factory
{
    protected $model = PixTransfer::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'bank_account_id' => BankAccount::factory(),
            'request_control_key' => $this->faker->uuid(),
            'status' => PixTransferStatusEnum::PENDING,
            'pix_type' => PixTypeEnum::MANUAL,
            'recipient_data' => [
                'ispb' => $this->faker->numerify('########'),
                'account_branch' => $this->faker->numerify('####'),
                'account_number' => $this->faker->numerify('#########'),
                'account_digit' => $this->faker->numerify('#'),
                'account_type' => 'checking_account',
                'name' => $this->faker->name(),
                'document_number' => $this->faker->numerify('###########'),
            ],
            'amount' => null,
            'pix_message' => null,
            'latitude' => null,
            'longitude' => null,
            'password_hash' => null,
            'tfa_type' => null,
            'tfa_token' => null,
            'device_infos' => null,
            'completed_at' => null,
            'failed_at' => null,
            'cancelled_at' => null,
            'confirmed_at' => null,
            'error_message' => null,
            'is_favorite' => false,
            'favorite_name' => null,
        ];
    }

    /**
     * Indicate that the PIX transfer is completed.
     */
    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => PixTransferStatusEnum::COMPLETED,
            'completed_at' => now(),
            'confirmed_at' => now()->subMinutes(5),
        ]);
    }

    /**
     * Indicate that the PIX transfer is processing.
     */
    public function processing(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => PixTransferStatusEnum::PROCESSING,
            'confirmed_at' => now()->subMinutes(2),
        ]);
    }

    /**
     * Indicate that the PIX transfer has failed.
     */
    public function failed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => PixTransferStatusEnum::FAILED,
            'failed_at' => now(),
            'error_message' => 'Erro simulado para teste',
        ]);
    }

    /**
     * Indicate that the PIX transfer is cancelled.
     */
    public function cancelled(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => PixTransferStatusEnum::CANCELLED,
            'cancelled_at' => now(),
        ]);
    }

    /**
     * Indicate that the PIX transfer has amount and password set.
     */
    public function withAmountAndPassword(): static
    {
        return $this->state(fn (array $attributes) => [
            'amount' => $this->faker->randomFloat(2, 1, 10000),
            'pix_message' => $this->faker->sentence(),
            'latitude' => $this->faker->latitude(),
            'longitude' => $this->faker->longitude(),
            'password_hash' => Hash::make('password123'),
            'tfa_type' => $this->faker->randomElement(TfaTypeEnum::cases()),
        ]);
    }

    /**
     * Set specific bank account for the PIX transfer.
     */
    public function forBankAccount(BankAccount $bankAccount): static
    {
        return $this->state(fn (array $attributes) => [
            'bank_account_id' => $bankAccount->id,
        ]);
    }

    /**
     * Set specific recipient data.
     */
    public function withRecipient(array $recipientData): static
    {
        return $this->state(fn (array $attributes) => [
            'recipient_data' => array_merge($attributes['recipient_data'], $recipientData),
        ]);
    }

    /**
     * Set PIX type to QR Code.
     */
    public function qrCode(): static
    {
        return $this->state(fn (array $attributes) => [
            'pix_type' => PixTypeEnum::QR_CODE,
        ]);
    }

    /**
     * Set PIX type to PIX Key.
     */
    public function pixKey(): static
    {
        return $this->state(fn (array $attributes) => [
            'pix_type' => PixTypeEnum::PIX_KEY,
        ]);
    }
}
