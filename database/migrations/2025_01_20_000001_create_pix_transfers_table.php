<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;
use MongoDB\Laravel\Schema\Blueprint;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pix_transfers', function (Blueprint $collection) {
            $collection->uuid('pub_id');
            $collection->index('bank_account_id');
            $collection->index('request_control_key');
            $collection->index('status');
            $collection->index('pix_type');
            $collection->index('created_at');
            $collection->index('completed_at');
            $collection->index('failed_at');
            $collection->index('cancelled_at');
            $collection->index('confirmed_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pix_transfers', function (Blueprint $collection) {
            $collection->drop();
        });
    }
};
