<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;
use MongoDB\Laravel\Schema\Blueprint;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('device_infos', function (Blueprint $collection) {
            $collection->uuid('pub_id');
            $collection->index('pix_transfer_id');
            $collection->index('device_id');
            $collection->index('unique_id');
            $collection->index('ip_address');
            $collection->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('device_infos', function (Blueprint $collection) {
            $collection->drop();
        });
    }
};
