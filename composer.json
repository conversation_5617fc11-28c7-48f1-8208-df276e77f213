{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.4", "ext-bcmath": "*", "ext-ctype": "*", "ext-curl": "*", "ext-fileinfo": "*", "ext-hash": "*", "ext-json": "*", "ext-mbstring": "*", "ext-mongodb": "*", "ext-openssl": "*", "ext-pdo": "*", "ext-tokenizer": "*", "ext-xml": "*", "ext-zend-opcache": "*", "ext-zip": "*", "barryvdh/laravel-dompdf": "^2.1", "cloudinary-labs/cloudinary-laravel": "^2.0", "fakerphp/faker": "^1.19", "giggsey/libphonenumber-for-php": "^8.13", "guzzlehttp/guzzle": "^7.2", "intervention/image": "^3.0", "jelix/version": "^2.0", "laravel-notification-channels/onesignal": "^2.5", "laravel/framework": "^10.48", "laravel/horizon": "^5.12", "laravel/sanctum": "^3.3", "laravel/tinker": "^2.8", "laravel/ui": "^4.2", "league/flysystem-aws-s3-v3": "^3.0", "lucascudo/laravel-pt-br-localization": "^1.1", "maatwebsite/excel": "^3.1", "mongodb/laravel-mongodb": "~5.3.0", "mongodb/mongodb": "^1.21|^2", "phpoffice/phpspreadsheet": "^1.18", "phpunit/phpunit": "^10.0", "pusher/pusher-php-server": "^7.2", "sentry/sentry": "^4.0", "sentry/sentry-laravel": "^4.0", "simplesoftwareio/simple-qrcode": "^4.2", "spatie/browsershot": "^4.0", "spatie/laravel-activitylog": "^4.7", "spatie/laravel-backup": "^8.1", "spatie/pdf-to-image": "^2.2", "tymon/jwt-auth": "^2.0", "web-token/jwt-framework": "^3.4"}, "require-dev": {"barryvdh/laravel-ide-helper": "^3.0", "ergebnis/phpunit-slow-test-detector": "^2.19", "laravel/sail": "^1.19", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "spatie/laravel-ignition": "^2.0"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"composer/package-versions-deprecated": true, "php-http/discovery": true}}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "stable", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"], "post-update-cmd": ["@php artisan horizon:publish --ansi", "@php artisan vendor:publish --tag=laravel-assets --ansi --force"]}}