APP_NAME="App Corretor Cury"
APP_ENV=local
APP_DEBUG=true
APP_URL=http://corretor-cury.localhost:8088
APP_PORT=8088
APP_KEY=base64:reDwFZTtDZ+jXtLO+PARbKe8JpUS2j30OQHsnWBbmNA=

BAAS_PROVIDER=qi_tech

API_KEY=OPJ2LxPOcQDBsQOrfPy65U5xhzyTE.ZP

LOG_CHANNEL=stack
LOG_LEVEL=debug

DB_CONNECTION=mongodb
DB_HOST=mongodb
DB_PORT=27017
DB_DATABASE=cury_corretor_test
DB_USERNAME=root
DB_PASSWORD=root

DATABASE_CURY_SITE_URL=
DB_CURY_SITE_HOST=cury-website_mysql
DB_CURY_SITE_PORT=3306
DB_CURY_SITE_DATABASE=cury
DB_CURY_SITE_USERNAME=root
DB_CURY_SITE_PASSWORD=root
DB_CURY_SITE_SOCKET=

BROADCAST_DRIVER=pusher-soketi
#LARAVEL_WEBSOCKETS_PORT=6001
#LARAVEL_WEBSOCKETS_SSL_VERIFY_PEER=false
#LARAVEL_WEBSOCKETS_SCHEME=http
#LARAVEL_WEBSOCKETS_USETLS=false

CACHE_DRIVER=redis
CACHE_PREFIX=cache

QUEUE_CONNECTION=redis
QUEUE_FAILED_DRIVER=mongodb

SESSION_DRIVER=file
SESSION_LIFETIME=120

REDIS_HOST=redis
REDIS_PASSWORD=null
REDIS_PORT=6379

REDIS_QUEUE_DB=0
REDIS_CACHE_DB=1
REDIS_HORIZON_DB=2
REDIS_DB=3

MAIL_DRIVER=smtp
MAIL_HOST=smtp.mailtrap.io
MAIL_PORT=2525
MAIL_USERNAME=
MAIL_PASSWORD=
MAIL_ENCRYPTION=tls
MAIL_EHLO_DOMAIN=corretor-cury.localhost

MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"
MAIL_REPLYTO_ADDRESS=<EMAIL>
MAIL_REPLYTO_NAME="Reply Name"

MAIL_CRIACORRET_TI_CURY="<EMAIL>"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=sa-east-1
AWS_BUCKET=

# Soketi (local)
PUSHER_APP_ID=soketi-local-id
PUSHER_APP_KEY=soketi-local-key
PUSHER_APP_SECRET=soketi-local-secret
PUSHER_HOST=host.docker.internal
PUSHER_PORT=6001
PUSHER_SCHEME=http
WEBSOCKETS_CLIENT_HOST=127.0.0.1
WEBSOCKETS_CLIENT_PORT=6001

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
#MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

CORRETOR_LOGIN_ATIVO=true
GESTOR_LOGIN_ATIVO=true

JWT_SECRET=MHkjfoYMbALWuQ7Fj6ZFSZcGqYqsnW1gFn7btBoCvsfv31F5dfK0P3p83tN9XjnD
JWT_PUBLIC_KEY=file:///var/www/jwt_rsa
JWT_PRIVATE_KEY=file:///var/www/jwt_rsa.pub

ACTIVITY_LOGGER_ENABLED=false

# OneSignal - App Corretor - Homolog e produção
ONESIGNAL_APP_ID=
ONESIGNAL_REST_API_KEY=

# OneSignal - App Gestor - Homolog e produção
ONESIGNAL_GESTOR_APP_ID=
ONESIGNAL_GESTOR_REST_API_KEY=

# Prefixo no ID do onesignal (usar em ambientes que não forem production)
ONESIGNAL_EXTERNAL_ID_PREFIX=homolog-

# Disk GDrive
GOOGLE_DRIVE_CLIENT_ID=
GOOGLE_DRIVE_CLIENT_SECRET=
GOOGLE_DRIVE_REFRESH_TOKEN=
# GOOGLE_DRIVE_FOLDER_ID=

# Cloudinary (ambiente de testes)
CLOUDINARY_URL=cloudinary://679286532626324:J1PLu4sukI66zSpcF3O9szlwlR4@dnihnzcqg
CLOUDINARY_API_SECRET=J1PLu4sukI66zSpcF3O9szlwlR4
CLOUDINARY_FOLDER_ARQUIVOS="app.cury.local/Arquivos"

# Acessos Reporte Diário
REPORTE_DIARIO_URL=http://nginx/plantao/show-reporte-diario
REPORTE_DIARIO_KEY=Drt2WGx3XEhErAV1cg1vbrHM8eRrxegJ

# Enviar atualizações de Corretores/Gestores para a MOP
MOP_WEBHOOK=https://webhook.site/53cf44ee-6a3c-4ff7-ab9b-22e243dd86fe
MOP_WEBHOOK_TOKEN=6e3925983a5b575612e3d768418b5d9b87c6ba8c

# Backups
BACKUP_ARCHIVE_PASSWORD=

# FFID - LOCAL
FFID_TOKEN=35247eebacb9491ab6e5c2f569e0d09a692bfb2bd33f1e8a17525bdbc74bcf77
FFID_WEBHOOK_PRODUTOS=https://webhook.site/4f6fbb1f-79e4-4f05-b341-97cd0b1d497b
FFID_WEBHOOK_USUARIOS=https://webhook.site/4f6fbb1f-79e4-4f05-b341-97cd0b1d497b
FFID_API_URL=https://api.ffid.io
FFID_API_KEY=
FFID_API_SECRET=
FFID_EMAIL_LEADS_INVALIDOS="<EMAIL>,<EMAIL>"
FFID_WEBHOOK_KEY=

# CONTELE - LOCAL
CONTELE_BASE_URL=https://sms.comtele.com.br/api/v2/
CONTELE_API_KEY=

# Salesforce (produção)
SALESFORCE_BASE_URL=
SALESFORCE_CLIENT_ID=
SALESFORCE_CLIENT_SECRET=
SALESFORCE_USERNAME=
SALESFORCE_PASSWORD=
SALESFORCE_SECRET_TOKEN=
SALESFORCE_VERSION=
SALESFORCE_USER_ID=

# Salesforce (homolog)
SALESFORCE_HOMOLOG_BASE_URL=
SALESFORCE_HOMOLOG_CLIENT_ID=
SALESFORCE_HOMOLOG_CLIENT_SECRET=
SALESFORCE_HOMOLOG_USERNAME=
SALESFORCE_HOMOLOG_PASSWORD=
SALESFORCE_HOMOLOG_SECRET_TOKEN=
SALESFORCE_HOMOLOG_VERSION=
SALESFORCE_HOMOLOG_USER_ID=

# Salesforce (Contas)
SALESFORCE_CONTAS_BASE_URL="${SALESFORCE_HOMOLOG_BASE_URL}"
SALESFORCE_CONTAS_CLIENT_ID="${SALESFORCE_HOMOLOG_CLIENT_ID}"
SALESFORCE_CONTAS_CLIENT_SECRET="${SALESFORCE_HOMOLOG_CLIENT_SECRET}"
SALESFORCE_CONTAS_USERNAME="${SALESFORCE_HOMOLOG_USERNAME}"
SALESFORCE_CONTAS_PASSWORD="${SALESFORCE_HOMOLOG_PASSWORD}"
SALESFORCE_CONTAS_SECRET_TOKEN="${SALESFORCE_HOMOLOG_SECRET_TOKEN}"
SALESFORCE_CONTAS_VERSION="${SALESFORCE_HOMOLOG_VERSION}"
SALESFORCE_CONTAS_USER_ID="${SALESFORCE_HOMOLOG_USER_ID}"

# Salesforce - Token Webhook
SALESFORCE_API_TOKEN=

# Salesforce - Repasses
SALESFORCE_REPASSES_REPORT_ID=
SALESFORCE_REPASSES_UPDATE_ENABLED=0

CONTAS_URL_CLIENTE="https://homolog-plantao.cury.net/contas/cadastro/"

# MOST
MOST_API_URL=https://mostqiapi.com
MOST_API_KEY=

PROMETHEUS_URL=http://corretor-cury.localhost:9090

# Disparador de Leads
DISPARADOR_LEADS_WEBHOOK_KEY=123

# OpenAI (ChatGPT)
OPENAI_URL=https://api.openai.com/v1/chat/completions
OPENAI_API_KEY=

APP_PASSWORD_MASTER=

LOAD_AVERAGE_OVERLOADED=50

SENTRY_LARAVEL_DSN=
SENTRY_RELEASE=
SENTRY_ENVIRONMENT=

WWWGROUP=1000
WWWUSER=1000

# Soketi
SOKETI_DEBUG=1
SOKETI_DB_MYSQL_HOST=mysql
SOKETI_DB_MYSQL_PORT=3306
SOKETI_DB_MYSQL_USERNAME=root
SOKETI_DB_MYSQL_PASSWORD=root
SOKETI_DB_MYSQL_DATABASE=cury_app_websockets
SOKETI_DB_POOLING_ENABLED=true
SOKETI_APP_MANAGER_DRIVER=mysql
SOKETI_APP_MANAGER_MYSQL_VERSION=8.0
SOKETI_APP_MANAGER_MYSQL_USE_V2=true
SOKETI_APP_MANAGER_CACHE_ENABLED=true
SOKETI_APP_MANAGER_CACHE_TTL=600
SOKETI_METRICS_ENABLED=1
SOKETI_USER_AUTHENTICATION_TIMEOUT=5000
SOKETI_SSL_CERT=
SOKETI_SSL_KEY=
SOKETI_SSL_PASS=
SOKETI_SSL_CA=

LEADS_SEND_EXPIRED_TO_BOLSAO=true

BOLSAO_VIRTUAL_LIMITE_LEADS_APP=20

# BaaS (QiTech) #
QI_TECH_BAAS_BASE_URL=https://api-auth.sandbox.qitech.app
QI_TECH_CAAS_BASE_URL=https://api.sandbox.caas.qitech.app
QI_TECH_CERTIFIQI_BASE_URL=https://api.sandbox.certifiqi.com.br
QI_TECH_API_KEY=
QI_TECH_PRIVATE_KEY=
QI_TECH_PUBLIC_KEY=
QI_TECH_OCR_API_KEY=
QI_TECH_OCR_MOBILE_TOKEN=
QI_TECH_FACE_RECON_API_KEY=
QI_TECH_FACE_RECON_MOBILE_TOKEN=
QI_TECH_DEVICE_SCAN_MOBILE_TOKEN=
QI_TECH_ONBOARDING_API_KEY=
QI_TECH_ONBOARDING_SIGNATURE_KEY=
QI_TECH_TERMS_PATH=
QI_TECH_TERMS_URL=
QI_TECH_WEBHOOK_VALID_TIMESTAMP_MINUTES=5
BAAS_STATEMENT_DEFAULT_DAYS=90
