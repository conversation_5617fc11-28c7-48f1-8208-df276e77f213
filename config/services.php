<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
        'scheme' => 'https',
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'api' => [
        'key' => env('API_KEY'),
    ],

    'onesignal' => [
        'app_id' => env('ONESIGNAL_APP_ID'),
        'rest_api_key' => env('ONESIGNAL_REST_API_KEY'),
        'external_id_prefix' => env('ONESIGNAL_EXTERNAL_ID_PREFIX', ''),
    ],

    'onesignal_gestor' => [
        'app_id' => env('ONESIGNAL_GESTOR_APP_ID'),
        'rest_api_key' => env('ONESIGNAL_GESTOR_REST_API_KEY'),
    ],

    'mop' => [
        'webhook' => env('MOP_WEBHOOK'),
        'token' => env('MOP_WEBHOOK_TOKEN'),
    ],

    'reporte-diario' => [
        'url' => env('REPORTE_DIARIO_URL'),
        'key' => env('REPORTE_DIARIO_KEY'),
    ],

    'ffid' => [
        'token' => env('FFID_TOKEN'),
        'webhook-produtos' => env('FFID_WEBHOOK_PRODUTOS'),
        'webhook-usuarios' => env('FFID_WEBHOOK_USUARIOS'),
        'api-url' => env('FFID_API_URL'),
        'api-key' => env('FFID_API_KEY'),
        'api-secret' => env('FFID_API_SECRET'),
        'emails_leads_invalidos' => env('FFID_EMAIL_LEADS_INVALIDOS', ''),
        'webhook-key' => env('FFID_WEBHOOK_KEY', ''),
    ],

    'leads' => [
        'send-expired-to-bolsao' => env('LEADS_SEND_EXPIRED_TO_BOLSAO', true),
        'bolsao-virtual-limite-leads-app' => env('BOLSAO_VIRTUAL_LIMITE_LEADS_APP', 20),
    ],

    'salesforce' => [
        'base_url' => env('SALESFORCE_BASE_URL'),
        'client_id' => env('SALESFORCE_CLIENT_ID'),
        'client_secret' => env('SALESFORCE_CLIENT_SECRET'),
        'username' => env('SALESFORCE_USERNAME'),
        'password' => env('SALESFORCE_PASSWORD'),
        'secret_token' => env('SALESFORCE_SECRET_TOKEN'),
        'version' => env('SALESFORCE_VERSION'),
        'user_id' => env('SALESFORCE_USER_ID'),
        'api_token' => env('SALESFORCE_API_TOKEN'),
        'repasses' => [
            'report_id' => env('SALESFORCE_REPASSES_REPORT_ID'),
            'update_enabled' => env('SALESFORCE_REPASSES_UPDATE_ENABLED'),
        ],
    ],

    'salesforce-contas' => [
        'base_url' => env('SALESFORCE_CONTAS_BASE_URL'),
        'client_id' => env('SALESFORCE_CONTAS_CLIENT_ID'),
        'client_secret' => env('SALESFORCE_CONTAS_CLIENT_SECRET'),
        'username' => env('SALESFORCE_CONTAS_USERNAME'),
        'password' => env('SALESFORCE_CONTAS_PASSWORD'),
        'secret_token' => env('SALESFORCE_CONTAS_SECRET_TOKEN'),
        'version' => env('SALESFORCE_CONTAS_VERSION'),
        'user_id' => env('SALESFORCE_CONTAS_USER_ID'),
    ],

    'run-backup' => env('RUN_BACKUP', false),

    'openai' => [
        'api_url' => env('OPENAI_URL', 'https://api.openai.com/v1/chat/completions'),
        'api_key' => env('OPENAI_API_KEY'),
    ],

    'most' => [
        'api_url' => env('MOST_API_URL', 'https://mostqiapi.com'),
        'api_key' => env('MOST_API_KEY'),
    ],

    'contas' => [
        'url_cliente' => env('CONTAS_URL_CLIENTE', 'https://plantao.cury.net/contas/'),
    ],

    'disparador-leads' => [
        'webhook-key' => env('DISPARADOR_LEADS_WEBHOOK_KEY', ''),
    ],

    'server' => [
        'load-average-overloaded' => env('LOAD_AVERAGE_OVERLOADED', 50),
    ],

    'prometheus' => [
        'url' => env('PROMETHEUS_URL', 'https://prometheus-app.cury.net:9090'),
    ],

    'bank' => [
        'provider' => env('BAAS_PROVIDER', 'qi_tech'),
        'statement_default_days' => env('BAAS_STATEMENT_DEFAULT_DAYS', 90),
        'qi_tech' => [
            'base_baas_url' => env('QI_TECH_BAAS_BASE_URL'),
            'base_caas_url' => env('QI_TECH_CAAS_BASE_URL'),
            'base_certifiqi_url' => env('QI_TECH_CERTIFIQI_BASE_URL'),
            'api_key' => env('QI_TECH_API_KEY'),
            'api_private_key' => env('QI_TECH_PRIVATE_KEY'),
            'api_public_key' => env('QI_TECH_PUBLIC_KEY'),
            'ocr' => [
                'api_key' => env('QI_TECH_OCR_API_KEY'),
                'mobile_token' => env('QI_TECH_OCR_MOBILE_TOKEN'),
            ],
            'face_recon' => [
                'api_key' => env('QI_TECH_FACE_RECON_API_KEY'),
                'mobile_token' => env('QI_TECH_FACE_RECON_MOBILE_TOKEN'),
            ],
            'device_scan' => [
                'mobile_token' => env('QI_TECH_DEVICE_SCAN_MOBILE_TOKEN'),
            ],
            'onboarding' => [
                'api_key' => env('QI_TECH_ONBOARDING_API_KEY'),
                'signature_key' => env('QI_TECH_ONBOARDING_SIGNATURE_KEY'),
            ],
            'terms_path' => env('QI_TECH_TERMS_PATH'),
            'terms_url' => env('QI_TECH_TERMS_URL'),
            'webhook' => [
                'valid_timestamp_minutes' => env('QI_TECH_WEBHOOK_VALID_TIMESTAMP_MINUTES', 5),
            ],
        ],
    ],

];
