{"info": {"_postman_id": "ccd7e3fe-1a42-47d5-98d9-7caaf791a0ce", "name": "<PERSON><PERSON>tor (App Corretor)", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "291079", "_collection_link": "https://curycliente-4345.postman.co/workspace/Cury-Workspace~c2853824-833a-4060-8f18-2a3a92f5bc47/collection/291079-ccd7e3fe-1a42-47d5-98d9-7caaf791a0ce?action=share&source=collection_link&creator=291079"}, "item": [{"name": "Pagadoria", "item": [{"name": "Index", "request": {"method": "GET", "header": [], "url": {"raw": "{{url}}/app/pagadoria", "host": ["{{url}}"], "path": ["app", "pagadoria"]}, "description": "Retorna dados para a home de pagadoria, no formato:\n\n```json\n{\n    \"processos\": [\n        {\n            \"processo\": {\n                \"status\": \"processos-com-pendencias\",\n                \"titulo\": \"Processos com Pendências\"\n            },\n            \"total\": 4\n        },\n        {\n            \"processo\": {\n                \"status\": \"pronto-para-enviar\",\n                \"titulo\": \"Pronto para Enviar\"\n            },\n            \"total\": 3\n        },\n        {\n            \"processo\": {\n                \"status\": \"em-processo-de-pagamento\",\n                \"titulo\": \"Em Processo de Pagamento\"\n            },\n            \"total\": 2\n        },\n        {\n            \"processo\": {\n                \"status\": \"comissoes-pagas\",\n                \"titulo\": \"Comissões Pagas\"\n            },\n            \"total\": 1\n        }\n    ]\n}\n```"}, "response": []}, {"name": "Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{url}}/app/pagadoria/status/processos-com-pendencias", "host": ["{{url}}"], "path": ["app", "pagadoria", "status", "processos-com-pendencias"]}, "description": "Retorna dados de um status específico. O status pode ser 'processos-com-pendencias', 'pronto-para-enviar',  'em-processo-de-pagamento' e 'comissoes-pagas'.\n\n```json\n{\n    \"processos\": [\n        {\n            \"processo\": {\n                \"status\": \"processos-com-pendencias\",\n                \"titulo\": \"Processos com Pendências\"\n            },\n            \"regional\": {\n                \"id\": \"2f8cc843-82ba-4599-a2fd-882bf8398488\",\n                \"nome\": \"São Paulo\",\n                \"slug\": \"sp\"\n            },\n            \"cvc\": \"CVC-59301\",\n            \"data\": \"2022-03-25\",\n            \"nome\": \"<PERSON>uilherme de Souza Lira Sobrinho\",\n            \"produto\": \"Imóvel Dr. Violeta Ramires D'ávila\"\n        },\n        {\n            \"processo\": {\n                \"status\": \"processos-com-pendencias\",\n                \"titulo\": \"Processos com Pendências\"\n            },\n            \"regional\": {\n                \"id\": \"2f8cc843-82ba-4599-a2fd-882bf8398488\",\n                \"nome\": \"São Paulo\",\n                \"slug\": \"sp\"\n            },\n            \"cvc\": \"CVC-76464\",\n            \"data\": \"2022-03-17\",\n            \"nome\": \"Luara Duarte Filho\",\n            \"produto\": \"Imóvel Ornela Mascarenhas\"\n        },\n        {\n            \"processo\": {\n                \"status\": \"processos-com-pendencias\",\n                \"titulo\": \"Processos com Pendências\"\n            },\n            \"regional\": {\n                \"id\": \"2f8cc843-82ba-4599-a2fd-882bf8398488\",\n                \"nome\": \"São Paulo\",\n                \"slug\": \"sp\"\n            },\n            \"cvc\": \"CVC-03699\",\n            \"data\": \"2022-02-06\",\n            \"nome\": \"Sra. Luara Colaço Santiago\",\n            \"produto\": \"Imóvel Agustina Vieira Rivera\"\n        },\n        {\n            \"processo\": {\n                \"status\": \"processos-com-pendencias\",\n                \"titulo\": \"Processos com Pendências\"\n            },\n            \"regional\": {\n                \"id\": \"2f8cc843-82ba-4599-a2fd-882bf8398488\",\n                \"nome\": \"São Paulo\",\n                \"slug\": \"sp\"\n            },\n            \"cvc\": \"CVC-61799\",\n            \"data\": \"2022-04-23\",\n            \"nome\": \"Andres Lucas Rodrigues Neto\",\n            \"produto\": \"Imóvel Nádia Serna Sobrinho\"\n        }\n    ]\n}\n```"}, "response": []}, {"name": "CVC", "request": {"method": "GET", "header": [], "url": {"raw": "{{url}}/app/pagadoria/cvc/109613", "host": ["{{url}}"], "path": ["app", "pagadoria", "cvc", "109613"]}, "description": "Retorna dados da interna de um processo. Pode acessar tanto com /pagadoria/cvc/12345 como /pagadoria/cvc/CVC-12345.\n\nExistem campos \"titulo\" e \"subtitulo\". O subtitulo só é exibido no app se o status for \"processos-com-pendencias\" (ver no Figma).\n\n```json\n{\n    \"processo\": {\n        \"processo\": {\n            \"status\": \"processos-com-pendencias\",\n            \"titulo\": \"Processos com Pendências\"\n        },\n        \"regional\": {\n            \"id\": \"2f8cc843-82ba-4599-a2fd-882bf8398488\",\n            \"nome\": \"São Paulo\",\n            \"slug\": \"sp\"\n        },\n        \"cvc\": \"CVC-59301\",\n        \"data\": \"2022-03-25\",\n        \"nome\": \"Guilherme de Souza Lira Sobrinho\",\n        \"produto\": \"Imóvel Dr. Violeta Ramires D'ávila\",\n        \"titulo\": \"Última atualização:\",\n        \"subtitulo\": \"29/01/2022\",\n        \"texto\": \"PV-48140\\n                    <br><br>\\n                    Data da venda: 25/03/2022\\n                    <br><br>\\n                    Produto: Imóvel Dr. Violeta Ramires D'ávila\\n                    <br><br>\\n                    Torre: Torre 60\\n                    <br><br>\\n                    Unidade: Apto. 56\\n                    <br><br>\\n                    Nome: Guilherme de Souza Lira Sobrinho\\n                    <br><br>\\n                    Equipe: Cury Vendas - SP\\n                    <br><br>\\n                    Minuta Assinada: 16/04/2022\\n                    <br><br>\\n                    CVC validado em: 10/03/2022\\n                    <br><br>\\n                    Histórico: Sed rerum distinctio et nemo. Et sint nostrum totam porro blanditiis est et aut. Officia nobis sit et quia et molestias et. Amet est nesciunt enim sed eos modi neque.\",\n        \"atualizado_em\": \"2022-04-28\"\n    }\n}\n```"}, "response": []}]}, {"name": "Repasses", "item": [{"name": "Index", "request": {"method": "GET", "header": [], "url": {"raw": "{{url}}/app/repasses", "host": ["{{url}}"], "path": ["app", "repasses"]}, "description": "Retorna dados para a home de repasses, no formato:\n\n```json\n{\n    \"atualizado_em\": null,\n    \"processos\": [\n        {\n            \"processo\": {\n                \"status\": \"futuro-vencido\",\n                \"titulo\": \"Repasse Futuro Vencido\"\n            },\n            \"total\": 3\n        },\n        {\n            \"processo\": {\n                \"status\": \"pendencia\",\n                \"titulo\": \"Pendência para Repasse\"\n            },\n            \"total\": 4\n        },\n        {\n            \"processo\": {\n                \"status\": \"pro-soluto\",\n                \"titulo\": \"Pró-Soluto a ser negociado\"\n            },\n            \"total\": 2\n        },\n        {\n            \"processo\": {\n                \"status\": \"enviado-distrato\",\n                \"titulo\": \"Enviado para Reversão / Distrato\"\n            },\n            \"total\": 1\n        }\n    ]\n}\n```"}, "response": []}, {"name": "Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{url}}/app/repasses/status/futuro-vencido", "host": ["{{url}}"], "path": ["app", "repasses", "status", "futuro-vencido"]}, "description": "Retorna dados de um status específico. O status pode ser 'futuro-vencido', 'pendencia',  'pro-soluto' e 'enviado-distrato'.\n\n```json\n{\n    \"atualizado_em\": \"2022-08-15\",\n    \"processos\": [\n        {\n            \"processo\": {\n                \"status\": \"futuro-vencido\",\n                \"titulo\": \"Repasse Futuro Vencido\"\n            },\n            \"regional\": {\n                \"id\": \"2f8cc843-82ba-4599-a2fd-882bf8398488\",\n                \"nome\": \"São Paulo\",\n                \"slug\": \"sp\"\n            },\n            \"cvc\": \"CVC-93337\",\n            \"data\": \"2022-07-19\",\n            \"corretor\": \"<PERSON>\",\n            \"produto\": \"Imóvel Milene Fátima Dominato\",\n            \"pendencia\": \"libero consequatur dignissimos\"\n        },\n        {\n            \"processo\": {\n                \"status\": \"futuro-vencido\",\n                \"titulo\": \"Repasse Futuro Vencido\"\n            },\n            \"regional\": {\n                \"id\": \"2f8cc843-82ba-4599-a2fd-882bf8398488\",\n                \"nome\": \"São Paulo\",\n                \"slug\": \"sp\"\n            },\n            \"cvc\": \"CVC-35736\",\n            \"data\": \"2022-05-29\",\n            \"corretor\": \"Aaron Thiago_\",\n            \"produto\": \"Imóvel Dr. Edson Zamana Toledo\",\n            \"pendencia\": \"voluptates et autem\"\n        },\n        {\n            \"processo\": {\n                \"status\": \"futuro-vencido\",\n                \"titulo\": \"Repasse Futuro Vencido\"\n            },\n            \"regional\": {\n                \"id\": \"2f8cc843-82ba-4599-a2fd-882bf8398488\",\n                \"nome\": \"São Paulo\",\n                \"slug\": \"sp\"\n            },\n            \"cvc\": \"CVC-20548\",\n            \"data\": \"2022-05-31\",\n            \"corretor\": \"Aaron Thiago_\",\n            \"produto\": \"Imóvel Adriel Santiago Ferminiano\",\n            \"pendencia\": \"ullam harum voluptatem\"\n        }\n    ]\n}\n```"}, "response": []}, {"name": "CVC", "request": {"method": "GET", "header": [], "url": {"raw": "{{url}}/app/repasses/cvc/77484", "host": ["{{url}}"], "path": ["app", "repasses", "cvc", "77484"]}, "description": "Retorna dados da interna de um processo. Pode acessar tanto com /repasses/cvc/12345 como /repasses/cvc/CVC-12345.\n\n```json\n{\n    \"atualizado_em\": \"2022-08-15\",\n    \"processo\": {\n        \"processo\": {\n            \"status\": \"futuro-vencido\",\n            \"titulo\": \"Repasse Futuro Vencido\"\n        },\n        \"regional\": {\n            \"id\": \"2f8cc843-82ba-4599-a2fd-882bf8398488\",\n            \"nome\": \"São Paulo\",\n            \"slug\": \"sp\"\n        },\n        \"cvc\": \"CVC-93337\",\n        \"data\": \"2022-07-19\",\n        \"corretor\": \"<PERSON>\",\n        \"produto\": \"Imóvel Milene Fátima Dominato\",\n        \"pendencia\": \"libero consequatur dignissimos\",\n        \"titulo\": \"Pendência: libero consequatur dignissimos\",\n        \"texto\": \"PV: PV-83787\\n                    <br><br>\\n                    Data inclusão: 2022-07-19\\n                    <br><br>\\n                    Empreendimento: Imóvel Milene Fátima Dominato\\n                    <br><br>\\n                    Bloco: Bloco 30\\n                    <br><br>\\n                    Imóvel: Apto. 98\\n                    <br><br>\\n                    Nome do cliente: Janaina Sabrina Corona\\n                    <br><br>\\n                    Corretor: Aaron Thiago_\\n                    <br><br>\\n                    Gerente: Benjamin Elias\\n                    <br><br>\\n                    Superintendente: Maximiano Laura\\n                    <br><br>\\n                    Diretor: Rebeca Adriana\\n                    <br><br>\"\n    }\n}\n```"}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}, {"name": "<PERSON>r<PERSON><PERSON>", "item": [{"name": "Home", "request": {"method": "GET", "header": [], "url": {"raw": "{{url}}/app/arquivos", "host": ["{{url}}"], "path": ["app", "arquivos"]}}, "response": []}, {"name": "Pasta", "request": {"method": "GET", "header": [], "url": {"raw": "{{url}}/app/arquivos/8ad1601e-6eae-4625-862b-da05d227f98d", "host": ["{{url}}"], "path": ["app", "arquivos", "8ad1601e-6eae-4625-862b-da05d227f98d"]}}, "response": []}]}, {"name": "Rh", "item": [{"name": "Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{url}}/app/rh", "host": ["{{url}}"], "path": ["app", "rh"]}, "description": "Retorna informações de status do cadastro do user (usar para verificar se deve exibir aquela pré-home de status pendente ou com infos do RH)\n\n\"status\" pode ser: aprovado, reprovado, pendente\n\"status_infos\" retorna a mensagem que deve ser exibida\n\"enviar_novamente_enabled\": Se o status do user for \"reprovado\" e ele já tiver feito alterações nos dados, o valor é \"true\" e habilita o botão de enviar novamente.\n\n```json\n{\n    \"status\": \"aprovado\",\n    \"status_infos\": \"Seu cadastro já foi aprovado!\",\n    \"enviar_novamente_enabled\": false,\n    \"alteracoes_pendentes\": [\"dados-pessoais\", \"endereco\", \"documentos\", \"foto\"]\n}\n```"}, "response": []}, {"name": "Enviar cadastro para aprovação", "request": {"method": "POST", "header": [], "url": {"raw": "{{url}}/app/rh/cadastro", "host": ["{{url}}"], "path": ["app", "rh", "cadastro"]}, "description": "Se o status do user for \"reprovado\", chamar esse endpoint para enviar novamente para aprovação."}, "response": []}, {"name": "Check Apelido", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "apelido", "value": "Blá", "type": "text"}]}, "url": {"raw": "{{url}}/app/rh/cadastro/check-apelido", "host": ["{{url}}"], "path": ["app", "rh", "cadastro", "check-apelido"]}, "description": "Verifica se o apelido está disponível.\n\nRetorna status 200 se o apelido estiver disponível e 422 caso contrário.\n\n```json\n{\n    \"message\": \"Apelido disponível\",\n    \"status_cadastro\": null\n}\n```"}, "response": []}, {"name": "Check E-mail", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "email", "value": "<EMAIL>", "type": "text"}]}, "url": {"raw": "{{url}}/app/rh/cadastro/check-email", "host": ["{{url}}"], "path": ["app", "rh", "cadastro", "check-email"]}, "description": "Verifica se o e-mail está disponível.\n\nRetorna status 200 se o e-mail estiver disponível e 422 caso contrário.\n\n```json\n{\n    \"message\": \"E-mail disponível\",\n    \"status_cadastro\": null\n}\n```"}, "response": []}, {"name": "<PERSON><PERSON>", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "{{url}}/app/rh/cadastro/dados-pessoais", "host": ["{{url}}"], "path": ["app", "rh", "cadastro", "dados-pessoais"]}, "description": "Dados pessoais do user. Tem um campo \"_disabled\" para informar quais campos devem ser desabilitados no app.\n\n```json\n{\n    \"dados\": {\n        \"regional\": {\n            \"id\": \"2f8cc843-82ba-4599-a2fd-882bf8398488\",\n            \"nome\": \"São Paulo\",\n            \"slug\": \"sp\"\n        },\n        \"gerente\": {\n            \"id\": \"4bc6b822-ed3e-4467-9dde-fb151b9de36d\",\n            \"nome\": \"<PERSON><PERSON><PERSON> Lo<PERSON>\",\n            \"apelido\": \"<PERSON>\",\n            \"perfil\": \"gerente\"\n        },\n        \"canal\": \"PDV\",\n        \"nome\": \"<PERSON><PERSON>\",\n        \"apelido\": \"<PERSON>\",\n        \"email\": \"<EMAIL>\",\n        \"email_cury_vendas\": null,\n        \"telefone\": \"(42) 9761-65065\",\n        \"cpf\": \"989.061.090-67\",\n        \"data_nascimento\": \"1974-01-21\"\n    },\n    \"_disabled\": [\n        \"regional\",\n        \"gerente\",\n        \"canal\",\n        \"email_cury_vendas\",\n        \"cpf\"\n    ]\n}\n```"}, "response": []}, {"name": "<PERSON><PERSON>", "request": {"method": "PUT", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "nome", "value": "Nome", "type": "text"}, {"key": "apelido", "value": "Apelido", "description": "Apenas se o status_cadastro for pendente ou reprovado", "type": "text"}, {"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "telefone", "value": "(11) 99999-9999", "type": "text"}, {"key": "data_nascimento", "value": "1990-05-21", "type": "text"}]}, "url": {"raw": "{{url}}/app/rh/cadastro/dados-pessoais", "host": ["{{url}}"], "path": ["app", "rh", "cadastro", "dados-pessoais"]}, "description": "Dados pessoais do user. Tem um campo \"_disabled\" para informar quais campos devem ser desabilitados no app.\n\n```json\n{\n    \"dados\": {\n        \"regional\": {\n            \"id\": \"2f8cc843-82ba-4599-a2fd-882bf8398488\",\n            \"nome\": \"São Paulo\",\n            \"slug\": \"sp\"\n        },\n        \"gerente\": {\n            \"id\": \"4bc6b822-ed3e-4467-9dde-fb151b9de36d\",\n            \"nome\": \"<PERSON><PERSON><PERSON> Lo<PERSON>\",\n            \"apelido\": \"<PERSON>\",\n            \"perfil\": \"gerente\"\n        },\n        \"canal\": \"PDV\",\n        \"nome\": \"<PERSON><PERSON>\",\n        \"apelido\": \"<PERSON>\",\n        \"email\": \"<EMAIL>\",\n        \"email_cury_vendas\": null,\n        \"telefone\": \"(42) 9761-65065\",\n        \"cpf\": \"989.061.090-67\",\n        \"data_nascimento\": \"1974-01-21\"\n    },\n    \"_disabled\": [\n        \"regional\",\n        \"gerente\",\n        \"canal\",\n        \"email_cury_vendas\",\n        \"cpf\"\n    ]\n}\n```"}, "response": []}, {"name": "Endereço", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "{{url}}/app/rh/cadastro/endereco", "host": ["{{url}}"], "path": ["app", "rh", "cadastro", "endereco"]}, "description": "Endereço do user. Tem um campo \"_disabled\" para informar quais campos devem ser desabilitados no app.\n\n```json\n{\n    \"dados\": {\n        \"cep\": \"93425-448\",\n        \"logradouro\": \"Avenida Assunção\",\n        \"numero\": \"4\",\n        \"complemento\": null,\n        \"bairro\": \"Porto Noelí do Sul\",\n        \"cidade\": \"Violeta do Norte\",\n        \"uf\": \"RJ\",\n        \"comprovante_endereco_arquivo\": {\n            \"id\": \"17242eb4-3bb1-4a24-afbe-565c4f32ad08\",\n            \"tipo\": \"comprovante_endereco\",\n            \"label\": \"Nome do arquivo 2trwKPNH.jpg\",\n            \"created_at\": \"2020-12-14 20:14:12\"\n        }\n    },\n    \"_disabled\": []\n}\n```"}, "response": []}, {"name": "Endereço Copy", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "https://corretor-cury.local:4443/app/rh/documento/comprovante_endereco/preview", "protocol": "https", "host": ["corretor-cury", "local"], "port": "4443", "path": ["app", "rh", "documento", "comprovante_endereco", "preview"]}, "description": "Endereço do user. Tem um campo \"_disabled\" para informar quais campos devem ser desabilitados no app.\n\n```json\n{\n    \"dados\": {\n        \"cep\": \"93425-448\",\n        \"logradouro\": \"Avenida Assunção\",\n        \"numero\": \"4\",\n        \"complemento\": null,\n        \"bairro\": \"Porto Noelí do Sul\",\n        \"cidade\": \"Violeta do Norte\",\n        \"uf\": \"RJ\",\n        \"comprovante_endereco_arquivo\": {\n            \"id\": \"17242eb4-3bb1-4a24-afbe-565c4f32ad08\",\n            \"tipo\": \"comprovante_endereco\",\n            \"label\": \"Nome do arquivo 2trwKPNH.jpg\",\n            \"created_at\": \"2020-12-14 20:14:12\"\n        }\n    },\n    \"_disabled\": []\n}\n```"}, "response": []}, {"name": "Endereço", "request": {"method": "PUT", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "cep", "value": "93425-448", "type": "text"}, {"key": "logradouro", "value": "<PERSON><PERSON><PERSON>", "type": "text"}, {"key": "numero", "value": "4", "type": "text"}, {"key": "complemento", "value": "", "type": "text"}, {"key": "bairro", "value": "Porto Noelí do Sul", "type": "text"}, {"key": "cidade", "value": "Violeta do Norte", "type": "text"}, {"key": "uf", "value": "RJ", "type": "text"}, {"key": "comprovante_endereco_id", "value": "17242eb4-3bb1-4a24-afbe-565c4f32ad08", "type": "text"}]}, "url": {"raw": "{{url}}/app/rh/cadastro/endereco", "host": ["{{url}}"], "path": ["app", "rh", "cadastro", "endereco"]}, "description": "Atualiza endereço do user."}, "response": []}, {"name": "Documentos", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "{{url}}/app/rh/cadastro/documentos", "host": ["{{url}}"], "path": ["app", "rh", "cadastro", "documentos"]}, "description": "Documentos do user. Tem um campo \"_disabled\" para informar quais campos devem ser desabilitados no app.\n\n```json\n{\n    \"dados\": {\n        \"tipo_contrato\": \"credenciado\",\n        \"tipo_contrato_status\": \"Vencido\",\n        \"documento_numero\": \"40.206.913-8\",\n        \"documento_arquivo\": {\n            \"id\": \"7e3d2c33-1d61-40d6-833f-281f94190b0e\",\n            \"tipo\": \"documento\",\n            \"label\": \"Nome do arquivo AbMAIyNS.jpg\",\n            \"created_at\": \"2020-12-02 18:57:57\"\n        },\n        \"documento_verso_arquivo\": {\n            \"id\": \"40418da1-1eea-41bb-8e79-40dd9c054968\",\n            \"tipo\": \"documento\",\n            \"label\": \"EwzcFNLU8AEbr-A.jpeg\",\n            \"created_at\": \"2021-03-18 22:46:35\"\n        },\n        \"creci_numero\": \"38879.4841-2\",\n        \"creci_validade\": \"2021-09-14\",\n        \"creci_arquivo\": {\n            \"id\": \"f483d424-7336-4f45-bcc8-6614f719ab28\",\n            \"tipo\": \"creci\",\n            \"label\": \"Nome do arquivo tgx8NNd0.jpg\",\n            \"created_at\": \"2020-12-02 18:57:57\"\n        },\n        \"creci_verso_arquivo\": null\n    },\n    \"_disabled\": []\n}\n```"}, "response": []}, {"name": "Documentos", "request": {"method": "PUT", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "documento_numero", "value": "40.206.913-8", "type": "text"}, {"key": "documento_id", "value": "7e3d2c33-1d61-40d6-833f-281f94190b0e", "type": "text"}, {"key": "documento_verso_id", "value": "40418da1-1eea-41bb-8e79-40dd9c054968", "type": "text"}, {"key": "contrato_numero", "value": "38879.4841-2", "type": "text"}, {"key": "contrato_validade", "value": "2021-09-14", "type": "text"}, {"key": "contrato_id", "value": "f483d424-7336-4f45-bcc8-6614f719ab28", "type": "text"}, {"key": "contrato_verso_id", "value": "", "type": "text"}]}, "url": {"raw": "{{url}}/app/rh/cadastro/documentos", "host": ["{{url}}"], "path": ["app", "rh", "cadastro", "documentos"]}, "description": "Atualiza documentos do user.\n"}, "response": []}, {"name": "Foto", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "{{url}}/app/rh/cadastro/foto", "host": ["{{url}}"], "path": ["app", "rh", "cadastro", "foto"]}, "description": "Selfie enviada pelo usuário. Segue a mesma estrutura dos outros arquivos, e se o user não tiver foto retorna null:\n\n```json\n{\n    \"dados\": {\n        \"selfie_arquivo\": null\n    },\n    \"_disabled\": []\n}\n```"}, "response": []}, {"name": "Foto", "request": {"method": "PUT", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "selfie_id", "value": "", "type": "text"}]}, "url": {"raw": "{{url}}/app/rh/cadastro/foto", "host": ["{{url}}"], "path": ["app", "rh", "cadastro", "foto"]}, "description": "Atualiza selfie do usuário."}, "response": []}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "request": {"method": "PUT", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "password", "value": "", "type": "text"}, {"key": "password_new", "value": "", "type": "text"}, {"key": "password_new_confirm", "value": "", "type": "text"}]}, "url": {"raw": "{{url}}/app/rh/cadastro/senha", "host": ["{{url}}"], "path": ["app", "rh", "cadastro", "<PERSON><PERSON>a"]}, "description": "Atualiza senha do usuário."}, "response": []}]}, {"name": "Leads", "item": [{"name": "Lista", "request": {"method": "GET", "header": [], "url": {"raw": "{{url}}/app/leads?data=2024-10-21", "host": ["{{url}}"], "path": ["app", "leads"], "query": [{"key": "data", "value": "2024-10-21"}]}, "description": "Retorna lista de leads da data informado.\n\n<PERSON>aso existam leads, é retornado nesse formato:\n\n``` json\n{\n    \"totais\": {\n        \"ativados\": 1,\n        \"aguardando\": 2,\n        \"expirados\": 1,\n        \"geral\": 4\n    },\n    \"leads\": [\n        {\n            \"id\": \"37515a33-2838-4636-9a49-785d5074d481\",\n            \"origem\": \"whatsapp\",\n            \"cliente\": {\n                \"nome\": \"<PERSON><PERSON>\"\n            },\n            \"status\": \"aguardando\",\n            \"plantao\": {\n                \"id\": \"5ead9847-9246-4fec-bca2-9fc200e2b17d\",\n                \"imovel_nome\": \"Imovel Cláudio 18827549\",\n                \"checkin_manual\": false\n            },\n            \"disparo\": {\n                \"data\": \"2023-03-19 11:57:52\"\n            },\n            \"validade\": {\n                \"data\": null,\n                \"duracao\": null\n            }\n        },\n        {\n            \"id\": \"0ff00dd4-5695-42c0-9f05-4b55d56fb479\",\n            \"origem\": \"whatsapp\",\n            \"cliente\": {\n                \"nome\": \"<PERSON>bgai<PERSON>\"\n            },\n            \"status\": \"aguardando\",\n            \"plantao\": {\n                \"id\": \"5ead9847-9246-4fec-bca2-9fc200e2b17d\",\n                \"imovel_nome\": \"Imovel Cláudio 18827549\",\n                \"checkin_manual\": false\n            },\n            \"disparo\": {\n                \"data\": \"2023-03-19 11:58:59\"\n            },\n            \"validade\": {\n                \"data\": null,\n                \"duracao\": null\n            }\n        },\n        {\n            \"id\": \"30f108ce-2994-43cf-a8bb-e20728c4bc37\",\n            \"origem\": \"whatsapp\",\n            \"cliente\": {\n                \"nome\": \"Jorge Gil\"\n            },\n            \"status\": \"ativado\",\n            \"plantao\": {\n                \"id\": \"5ead9847-9246-4fec-bca2-9fc200e2b17d\",\n                \"imovel_nome\": \"Imovel Cláudio 18827549\",\n                \"checkin_manual\": false\n            },\n            \"disparo\": {\n                \"data\": \"2023-03-19 11:59:08\"\n            },\n            \"validade\": {\n                \"data\": null,\n                \"duracao\": null\n            }\n        },\n        {\n            \"id\": \"935606d2-8df4-4552-ae76-4ec340f98833\",\n            \"origem\": \"whatsapp\",\n            \"cliente\": {\n                \"nome\": \"Lead expirado\"\n            },\n            \"status\": \"expirado\",\n            \"plantao\": {\n                \"id\": \"5ead9847-9246-4fec-bca2-9fc200e2b17d\",\n                \"imovel_nome\": \"Imovel Cláudio 18827549\",\n                \"checkin_manual\": false\n            },\n            \"disparo\": {\n                \"data\": \"2023-03-19 11:59:26\"\n            },\n            \"validade\": {\n                \"data\": null,\n                \"duracao\": null\n            }\n        }\n    ],\n    \"data\": \"2023-03-19\"\n}\n\n```\n\n**Observação:** Os campos de \"validade\" por enquanto vão retornar sempre null. Ele vai ser usado quando ativarmos a expiração de leads. Quando isso acontecer vai retornar algo como:\n\n``` json\n\"validade\": {\n                \"data\": \"2023-04-01 10:00:00\",\n                \"duracao\": \"40min\" //tempo que falta para a expiração do lead\n            }\n\n```\n\nE casa não hajam leads na data informada:\n\n``` json\n{\n    \"totais\": {\n        \"ativados\": 0,\n        \"aguardando\": 0,\n        \"expirados\": 0,\n        \"geral\": 0\n    },\n    \"leads\": [],\n    \"data\": \"2023-03-18\"\n}\n\n```"}, "response": []}, {"name": "Interna", "request": {"method": "GET", "header": [], "url": {"raw": "{{url}}/app/leads/0d0b450f-2808-45e8-a9f5-1ad746f0268b", "host": ["{{url}}"], "path": ["app", "leads", "0d0b450f-2808-45e8-a9f5-1ad746f0268b"]}, "description": "Interna do lead. Retorna 403 se o user logado não tiver acesso ao lead e 404 caso o lead não exista.\n\nRetorno para lead com status **\"aguardando\"**:\n\n``` json\n{\n    \"lead\": {\n        \"id\": \"37515a33-2838-4636-9a49-785d5074d481\",\n        \"origem\": \"whatsapp\",\n        \"cliente\": {\n            \"nome\": \"<PERSON><PERSON>\",\n            \"email\": \"<EMAIL>\",\n            \"celular\": \"(48) 90151-4581\",\n            \"avatar\": \"https://via.placeholder.com/200x200.png/00cc88?text=animals+et\"\n        },\n        \"status\": \"aguardando\",\n        \"plantao\": {\n            \"id\": \"5ead9847-9246-4fec-bca2-9fc200e2b17d\",\n            \"imovel_nome\": \"Imovel Cláudio 18827549\",\n            \"checkin_manual\": false\n        },\n        \"disparo\": {\n            \"data\": \"2023-03-19 11:57:52\"\n        },\n        \"validade\": {\n            \"data\": null,\n            \"duracao\": null\n        },\n        \"ativacao\": {\n            \"data\": null\n        }\n    }\n}\n\n```\n\nRetorno para lead com status **\"ativado\"**:\n\n``` json\n{\n    \"lead\": {\n        \"id\": \"30f108ce-2994-43cf-a8bb-e20728c4bc37\",\n        \"origem\": \"whatsapp\",\n        \"cliente\": {\n            \"nome\": \"Jorge Gil\",\n            \"email\": \"<EMAIL>\",\n            \"celular\": \"(45) 95226-3562\",\n            \"avatar\": \"https://via.placeholder.com/200x200.png/005555?text=animals+blanditiis\"\n        },\n        \"status\": \"ativado\",\n        \"plantao\": {\n            \"id\": \"5ead9847-9246-4fec-bca2-9fc200e2b17d\",\n            \"imovel_nome\": \"Imovel Cláudio 18827549\",\n            \"checkin_manual\": false\n        },\n        \"disparo\": {\n            \"data\": \"2023-03-19 11:59:08\"\n        },\n        \"validade\": {\n            \"data\": null,\n            \"duracao\": null\n        },\n        \"ativacao\": {\n            \"data\": \"2023-03-19 11:59:14\"\n        }\n    }\n}\n\n```\n\nRetorno para lead com status **\"expirado\"**:\n\n``` json\n{\n    \"lead\": {\n        \"id\": \"935606d2-8df4-4552-ae76-4ec340f98833\",\n        \"origem\": \"whatsapp\",\n        \"cliente\": {\n            \"nome\": \"Lead expirado\",\n            \"email\": null,\n            \"celular\": null,\n            \"avatar\": null\n        },\n        \"status\": \"expirado\",\n        \"plantao\": {\n            \"id\": \"5ead9847-9246-4fec-bca2-9fc200e2b17d\",\n            \"imovel_nome\": \"Imovel Cláudio 18827549\",\n            \"checkin_manual\": false\n        },\n        \"disparo\": {\n            \"data\": \"2023-03-19 11:59:26\"\n        },\n        \"validade\": {\n            \"data\": null,\n            \"duracao\": null\n        },\n        \"ativacao\": {\n            \"data\": null\n        }\n    }\n}\n\n```"}, "response": []}]}, {"name": "CRM", "item": [{"name": "Listagem de Clientes", "request": {"method": "GET", "header": [], "url": {"raw": "{{url}}/app/crm/clientes", "host": ["{{url}}"], "path": ["app", "crm", "clientes"]}}, "response": []}]}, {"name": "Repasses (Salesforce)", "item": [{"name": "Index", "request": {"method": "GET", "header": [], "url": {"raw": "{{url}}/app/repasses-salesforce", "host": ["{{url}}"], "path": ["app", "repasses-salesforce"]}, "description": "Retorna dados para a home de repasses, no formato:\n\n``` json\n{\n    \"atualizado_em\": \"23/04/2024 18:39\",\n    \"etapas\": [\n        {\n            \"slug\": \"pendencias-de-vendas\",\n            \"titulo\": \"Pendências de Vendas\",\n            \"class\": \"error\",\n            \"total\": 388\n        },\n        {\n            \"slug\": \"processos-em-andamento\",\n            \"titulo\": \"Processos em Andamento\",\n            \"class\": \"warning\",\n            \"total\": 369\n        },\n        {\n            \"slug\": \"processos-concluidos\",\n            \"titulo\": \"Processos Concluídos\",\n            \"class\": \"success\",\n            \"total\": 3329\n        }\n    ]\n}\n\n ```\n\nO slug vai ser usado para acessar a interna das etapas (/etapa/{slug})."}, "response": []}, {"name": "Etapa (pendência de vendas)", "request": {"method": "GET", "header": [], "url": {"raw": "{{url}}/app/repasses-salesforce/etapa/pendencias-de-vendas", "host": ["{{url}}"], "path": ["app", "repasses-salesforce", "etapa", "pendencias-de-vendas"]}, "description": "Retorna dados para uma etapa que possui subetapas, no formato:\n\n``` json\n{\n    \"atualizado_em\": \"23/04/2024 18:39\",\n    \"etapa\": {\n        \"slug\": \"pendencias-de-vendas\",\n        \"titulo\": \"Pendências de Vendas\",\n        \"class\": \"error\"\n    },\n    \"etapas\": {\n        \"repasse-futuro\": {\n            \"slug\": \"repasse-futuro\",\n            \"titulo\": \"Repasse Futuro\",\n            \"class\": \"error\",\n            \"total\": 5\n        },\n        \"pendencias-para-repasse\": {\n            \"slug\": \"pendencias-para-repasse\",\n            \"titulo\": \"Pendências Para Repasse\",\n            \"class\": \"error\",\n            \"total\": 52\n        },\n        \"enviado-para-reversao-distrato\": {\n            \"slug\": \"enviado-para-reversao-distrato\",\n            \"titulo\": \"Enviado Para Reversão / Distrato\",\n            \"class\": \"error\",\n            \"total\": 331\n        }\n    }\n}\n\n ```"}, "response": []}, {"name": "Etapa (processos em andamento)", "request": {"method": "GET", "header": [], "url": {"raw": "{{url}}/app/repasses-salesforce/etapa/processos-em-andamento", "host": ["{{url}}"], "path": ["app", "repasses-salesforce", "etapa", "processos-em-andamento"]}, "description": "Retorna dados para uma etapa que possui subetapas, no formato:\n\n``` json\n{\n    \"atualizado_em\": \"23/04/2024 18:39\",\n    \"etapa\": {\n        \"slug\": \"processos-em-andamento\",\n        \"titulo\": \"Processos em Andamento\",\n        \"class\": \"warning\"\n    },\n    \"etapas\": {\n        \"venda-nova-conferencia\": {\n            \"slug\": \"venda-nova-conferencia\",\n            \"titulo\": \"Venda Nova / Conferência\",\n            \"class\": \"warning\",\n            \"total\": 28\n        },\n        \"debito-fgts\": {\n            \"slug\": \"debito-fgts\",\n            \"titulo\": \"Débito FGTS\",\n            \"class\": \"warning\",\n            \"total\": 14\n        },\n        \"emissao-de-certificado-digital\": {\n            \"slug\": \"emissao-de-certificado-digital\",\n            \"titulo\": \"Emissão de Certificado Digital\",\n            \"class\": \"warning\",\n            \"total\": 30\n        },\n        \"critica-siopi\": {\n            \"slug\": \"critica-siopi\",\n            \"titulo\": \"Crítica SIOPI\",\n            \"class\": \"warning\",\n            \"total\": 28\n        },\n        \"analise-cehop\": {\n            \"slug\": \"analise-cehop\",\n            \"titulo\": \"Análise CEHOP\",\n            \"class\": \"warning\",\n            \"total\": 76\n        },\n        \"conforme\": {\n            \"slug\": \"conforme\",\n            \"titulo\": \"Conforme\",\n            \"class\": \"warning\",\n            \"total\": 177\n        }\n    }\n}\n\n ```"}, "response": []}, {"name": "Etapa (processos concluídos)", "request": {"method": "GET", "header": [], "url": {"raw": "{{url}}/app/repasses-salesforce/etapa/processos-concluidos", "host": ["{{url}}"], "path": ["app", "repasses-salesforce", "etapa", "processos-concluidos"]}, "description": "Retorna dados para uma etapa que NÃO possui subetapas. <PERSON><PERSON><PERSON> caso, as PVs já são listadas diretamente.\n\nApenas no caso da etapa \"Processos concluídos\" o campo `etapa_sub_app` será sempre null.\n\n**Importante:** Nessa listagem seria bom a gente ter uma paginação, porém isso ainda não existe no backend. Então pra não ter risco de quebrar quando a gente fizer, vamos fazer assim:\n\n\\- se não tiver parâmetro page: vai continuar retornando todos os dados dessa forma\n\n- se tiver parâmetro page: vai retornar dados paginados\n    \n\n``` json\n{\n    \"atualizado_em\": \"23/04/2024 18:39\",\n    \"processos\": [\n        {\n            \"regional\": {\n                \"id\": \"2f8cc843-82ba-4599-a2fd-882bf8398488\",\n                \"nome\": \"São Paulo\",\n                \"slug\": \"sp\"\n            },\n            \"pv\": \"PV-139451\",\n            \"etapa_salesforce\": \"Cancelado\",\n            \"prazo_conclusao\": \"25/04/2024\",\n            \"data_conclusao\": \"-\",\n            \"data_cancelamento\": \"20/04/2024\",\n            \"data_status_atual\": \"20/04/2024\",\n            \"etapa_app\": {\n                \"slug\": \"processos-concluidos\",\n                \"titulo\": \"Processos Concluídos\",\n                \"class\": \"success\"\n            },\n            \"etapa_sub_app\": null,\n            \"etapa_dias\": 4,\n            \"responsavel\": \"MORGANA\",\n            \"produto\": \"Yunes Park\",\n            \"torre\": \"Torre 01\",\n            \"unidade\": \"Apto 0206\",\n            \"motivo\": \"-\",\n            \"atualizado_em\": \"2024-04-20 12:58\"\n        },\n        {\n            \"regional\": {\n                \"id\": \"2f8cc843-82ba-4599-a2fd-882bf8398488\",\n                \"nome\": \"São Paulo\",\n                \"slug\": \"sp\"\n            },\n            \"pv\": \"PV-136806\",\n            \"etapa_salesforce\": \"Repasse Concluído\",\n            \"prazo_conclusao\": \"-\",\n            \"data_conclusao\": \"16/04/2024\",\n            \"data_cancelamento\": \"-\",\n            \"data_status_atual\": \"16/04/2024\",\n            \"etapa_app\": {\n                \"slug\": \"processos-concluidos\",\n                \"titulo\": \"Processos Concluídos\",\n                \"class\": \"success\"\n            },\n            \"etapa_sub_app\": null,\n            \"etapa_dias\": 8,\n            \"responsavel\": \"VIDIGAL\",\n            \"produto\": \"Guedala Park I\",\n            \"torre\": \"Torre 01\",\n            \"unidade\": \"Apto 1211\",\n            \"motivo\": \"-\",\n            \"atualizado_em\": \"2024-04-17 11:17\"\n        },\n        {\n            \"regional\": {\n                \"id\": \"2f8cc843-82ba-4599-a2fd-882bf8398488\",\n                \"nome\": \"São Paulo\",\n                \"slug\": \"sp\"\n            },\n            \"pv\": \"PV-136791\",\n            \"etapa_salesforce\": \"Cancelado\",\n            \"prazo_conclusao\": \"-\",\n            \"data_conclusao\": \"-\",\n            \"data_cancelamento\": \"13/04/2024\",\n            \"data_status_atual\": \"13/04/2024\",\n            \"etapa_app\": {\n                \"slug\": \"processos-concluidos\",\n                \"titulo\": \"Processos Concluídos\",\n                \"class\": \"success\"\n            },\n            \"etapa_sub_app\": null,\n            \"etapa_dias\": 11,\n            \"responsavel\": \"VIDIGAL\",\n            \"produto\": \"Cidade Jaguaré - Villa Eldorado\",\n            \"torre\": \"Torre 01\",\n            \"unidade\": \"Apto 0603\",\n            \"motivo\": \"-\",\n            \"atualizado_em\": \"2024-04-17 17:10\"\n        }\n    ]\n}\n\n ```"}, "response": []}, {"name": "Subetapa", "request": {"method": "GET", "header": [], "url": {"raw": "{{url}}/app/repasses-salesforce/etapa/pendencias-de-vendas/pendencias-para-repasse", "host": ["{{url}}"], "path": ["app", "repasses-salesforce", "etapa", "pendencias-de-vendas", "pendencias-para-repasse"]}, "description": "Retorna dados de uma subetapa, no formato:\n\n**Importante:** <PERSON><PERSON><PERSON> listagem seria bom a gente ter uma paginação, porém isso ainda não existe no backend. Então pra não ter risco de quebrar quando a gente fizer, vamos fazer assim:\n\n\\- se não tiver parâmetro page: vai continuar retornando todos os dados dessa forma\n\n- se tiver parâmetro page: vai retornar dados paginados\n    \n\n``` json\n{\n    \"atualizado_em\": \"23/04/2024 18:39\",\n    \"processos\": [\n        {\n            \"regional\": {\n                \"id\": \"2f8cc843-82ba-4599-a2fd-882bf8398488\",\n                \"nome\": \"São Paulo\",\n                \"slug\": \"sp\"\n            },\n            \"pv\": \"PV-140067\",\n            \"etapa_salesforce\": \"Pendência\",\n            \"prazo_conclusao\": \"-\",\n            \"data_conclusao\": \"-\",\n            \"data_cancelamento\": \"-\",\n            \"data_status_atual\": \"22/04/2024\",\n            \"etapa_app\": {\n                \"slug\": \"pendencias-de-vendas\",\n                \"titulo\": \"Pendências de Vendas\",\n                \"class\": \"error\"\n            },\n            \"etapa_sub_app\": {\n                \"slug\": \"pendencias-para-repasse\",\n                \"titulo\": \"Pendências Para Repasse\",\n                \"class\": \"error\"\n            },\n            \"etapa_dias\": 2,\n            \"responsavel\": \"ANNY\",\n            \"produto\": \"Urban Vila Maria II\",\n            \"torre\": \"Torre 02\",\n            \"unidade\": \"Apto 0806\",\n            \"motivo\": \"Aguardando formularios assinados\",\n            \"atualizado_em\": \"23/04/2024 14:17\"\n        },\n        {\n            \"regional\": {\n                \"id\": \"e6884c64-123b-47cd-8af8-e9c0d59df357\",\n                \"nome\": \"Rio de Janeiro\",\n                \"slug\": \"rj\"\n            },\n            \"pv\": \"PV-140058\",\n            \"etapa_salesforce\": \"Pendência\",\n            \"prazo_conclusao\": \"-\",\n            \"data_conclusao\": \"-\",\n            \"data_cancelamento\": \"-\",\n            \"data_status_atual\": \"22/04/2024\",\n            \"etapa_app\": {\n                \"slug\": \"pendencias-de-vendas\",\n                \"titulo\": \"Pendências de Vendas\",\n                \"class\": \"error\"\n            },\n            \"etapa_sub_app\": {\n                \"slug\": \"pendencias-para-repasse\",\n                \"titulo\": \"Pendências Para Repasse\",\n                \"class\": \"error\"\n            },\n            \"etapa_dias\": 2,\n            \"responsavel\": \"vitencor.rj\",\n            \"produto\": \"Heitor dos Prazeres - Condomínio Pierrot\",\n            \"torre\": \"Bloco 02\",\n            \"unidade\": \"Apto 0702\",\n            \"motivo\": \"formulários assinados; certidao de estado civil;\",\n            \"atualizado_em\": \"23/04/2024 11:10\"\n        },\n        {\n            \"regional\": {\n                \"id\": \"2f8cc843-82ba-4599-a2fd-882bf8398488\",\n                \"nome\": \"São Paulo\",\n                \"slug\": \"sp\"\n            },\n            \"pv\": \"PV-140039\",\n            \"etapa_salesforce\": \"Pendência\",\n            \"prazo_conclusao\": \"-\",\n            \"data_conclusao\": \"-\",\n            \"data_cancelamento\": \"-\",\n            \"data_status_atual\": \"22/04/2024\",\n            \"etapa_app\": {\n                \"slug\": \"pendencias-de-vendas\",\n                \"titulo\": \"Pendências de Vendas\",\n                \"class\": \"error\"\n            },\n            \"etapa_sub_app\": {\n                \"slug\": \"pendencias-para-repasse\",\n                \"titulo\": \"Pendências Para Repasse\",\n                \"class\": \"error\"\n            },\n            \"etapa_dias\": 2,\n            \"responsavel\": \"ANNY\",\n            \"produto\": \"Urban Vila Maria II\",\n            \"torre\": \"Torre 02\",\n            \"unidade\": \"Apto 0909\",\n            \"motivo\": \"Aguardando formularios assinados\",\n            \"atualizado_em\": \"23/04/2024 10:56\"\n        }\n    ]\n}\n\n ```"}, "response": []}, {"name": "Busca", "request": {"method": "GET", "header": [], "url": {"raw": "{{url}}/app/repasses-salesforce/busca/140067", "host": ["{{url}}"], "path": ["app", "repasses-salesforce", "busca", "140067"]}, "description": "Retorna dados do resultado de busca, no formato:\n\n``` json\n{\n    \"atualizado_em\": \"23/04/2024 18:39\",\n    \"processos\": [\n        {\n            \"regional\": {\n                \"id\": \"2f8cc843-82ba-4599-a2fd-882bf8398488\",\n                \"nome\": \"São Paulo\",\n                \"slug\": \"sp\"\n            },\n            \"pv\": \"PV-140067\",\n            \"etapa_salesforce\": \"Pendência\",\n            \"prazo_conclusao\": \"-\",\n            \"data_conclusao\": \"-\",\n            \"data_cancelamento\": \"-\",\n            \"data_status_atual\": \"22/04/2024\",\n            \"etapa_app\": {\n                \"slug\": \"pendencias-de-vendas\",\n                \"titulo\": \"Pendências de Vendas\",\n                \"class\": \"error\"\n            },\n            \"etapa_sub_app\": {\n                \"slug\": \"pendencias-para-repasse\",\n                \"titulo\": \"Pendências Para Repasse\",\n                \"class\": \"error\"\n            },\n            \"etapa_dias\": 2,\n            \"responsavel\": \"ANNY\",\n            \"produto\": \"Urban Vila Maria II\",\n            \"torre\": \"Torre 02\",\n            \"unidade\": \"Apto 0806\",\n            \"motivo\": \"Aguardando formularios assinados\",\n            \"atualizado_em\": \"23/04/2024 14:17\"\n        }\n    ]\n}\n\n ```"}, "response": []}, {"name": "Interna PV", "request": {"method": "GET", "header": [], "url": {"raw": "{{url}}/app/repasses-salesforce/pv/140067", "host": ["{{url}}"], "path": ["app", "repasses-salesforce", "pv", "140067"]}, "description": "Retorna dados da interna de uma PV, no formato:\n\n``` json\n{\n    \"atualizado_em\": \"23/04/2024 18:39\",\n    \"processo\": {\n        \"regional\": {\n            \"id\": \"2f8cc843-82ba-4599-a2fd-882bf8398488\",\n            \"nome\": \"São Paulo\",\n            \"slug\": \"sp\"\n        },\n        \"pv\": \"PV-140067\",\n        \"etapa_salesforce\": \"Pendência\",\n        \"prazo_conclusao\": \"-\",\n        \"data_conclusao\": \"-\",\n        \"data_cancelamento\": \"-\",\n        \"data_status_atual\": \"22/04/2024\",\n        \"etapa_app\": {\n            \"slug\": \"pendencias-de-vendas\",\n            \"titulo\": \"Pendências de Vendas\",\n            \"class\": \"error\"\n        },\n        \"etapa_sub_app\": {\n            \"slug\": \"pendencias-para-repasse\",\n            \"titulo\": \"Pendências Para Repasse\",\n            \"class\": \"error\"\n        },\n        \"etapa_dias\": 2,\n        \"responsavel\": \"ANNY\",\n        \"produto\": \"Urban Vila Maria II\",\n        \"torre\": \"Torre 02\",\n        \"unidade\": \"Apto 0806\",\n        \"motivo\": \"Aguardando formularios assinados\",\n        \"atualizado_em\": \"23/04/2024 14:17\",\n        \"texto\": \"<strong class=&#x27;preserveHtml&#x27; class=&#x27;preserveHtml&#x27;>Status:</b> Pendência\\n                    <strong class=&#x27;preserveHtml&#x27; class=&#x27;preserveHtml&#x27;>PV:</b> 140067\\n                    &#013;&#013;\\n                    <strong class=&#x27;preserveHtml&#x27; class=&#x27;preserveHtml&#x27;>Data do status atual:</b> 22/04/2024\\n                    &#013;&#013;\\n                    <strong class=&#x27;preserveHtml&#x27; class=&#x27;preserveHtml&#x27;>CCA:</b> \\n                    &#013;&#013;\\n                    <strong class=&#x27;preserveHtml&#x27; class=&#x27;preserveHtml&#x27;>Empreendimento:</b> Urban Vila Maria II\\n                    &#013;&#013;\\n                    <strong class=&#x27;preserveHtml&#x27; class=&#x27;preserveHtml&#x27;>Torre:</b> Torre 02\\n                    &#013;&#013;\\n                    <strong class=&#x27;preserveHtml&#x27; class=&#x27;preserveHtml&#x27;>Unidade:</b> Apto 0806\\n                    &#013;&#013;\\n                    <strong class=&#x27;preserveHtml&#x27; class=&#x27;preserveHtml&#x27;>Nome do cliente:</b> NÃO ESTÁ SALVANDO NOME DO CLIENTE\\n                    &#013;&#013;\\n                    <strong class=&#x27;preserveHtml&#x27; class=&#x27;preserveHtml&#x27;>Corretor:</b> ANNY\\n                    &#013;&#013;\\n                    <strong class=&#x27;preserveHtml&#x27; class=&#x27;preserveHtml&#x27;>Gerente:</b> Tainara Silva\\n                    &#013;&#013;\\n                    <strong class=&#x27;preserveHtml&#x27; class=&#x27;preserveHtml&#x27;>Superintendente:</b> Diego Pego de Jesus\\n                    &#013;&#013;\\n                    <strong class=&#x27;preserveHtml&#x27; class=&#x27;preserveHtml&#x27;>Diretor:</b> Sidney Frassei Raga\\n                    &#013;&#013;<strong class=&#x27;preserveHtml&#x27; class=&#x27;preserveHtml&#x27;>Motivo:</b> \\n                    &#013;&#013;\"\n    }\n}\n\n ```"}, "response": []}]}, {"name": "ApiT<PERSON> (Teste)", "item": [{"name": "Get Token", "event": [{"listen": "test", "script": {"exec": ["const responseJson = pm.response.json();", "pm.collectionVariables.set(\"token_teste\", responseJson.access_token);"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{url}}/get-teste-token-local", "host": ["{{url}}"], "path": ["get-teste-token-local"]}}, "response": []}, {"name": "Use Token (teste)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token_teste}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{url}}/use-teste-token-local", "host": ["{{url}}"], "path": ["use-teste-token-local"]}}, "response": []}, {"name": "Use Token (teste, guard inv<PERSON>lido)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token_teste}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{url}}/use-teste-token-local-guard-invalido", "host": ["{{url}}"], "path": ["use-teste-token-local-guard-invalido"]}}, "response": []}]}, {"name": "Webhooks", "item": [{"name": "FFID", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "// {\"lead_id\":\"test-test-millene-123a\",\"produto_id\":null,\"produto_nome\":null,\"object_lead\":\"{\\\"client_id\\\":\\\"5e4e8ec7f7473d775e1ab43b\\\",\\\"customer_id\\\":\\\"669182b102d2bf76d86223c8\\\",\\\"lead_id\\\":\\\"1727992717\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"phone\\\":\\\"+5521967622683\\\",\\\"score\\\":3.9,\\\"font\\\":\\\"api\\\",\\\"origin\\\":\\\"whatsapp\\\",\\\"score_data\\\":{\\\"e164\\\":\\\"+5521967622683\\\",\\\"formatted_phone\\\":\\\"(21) 96762-2683\\\",\\\"whatsapp\\\":true,\\\"last_seen\\\":0,\\\"avatar\\\":\\\"https:\\\\\\/\\\\\\/cdn.fulfilling.io\\\\\\/template\\\\\\/media\\\\\\/5e4e8ec7f7473d775e1ab43b\\\\\\/95b9ee6c9608f2c58b19eb048e497392.jpg\\\",\\\"phone_type\\\":\\\"MOBILE\\\",\\\"wa_name\\\":\\\"Carol Batista\\\",\\\"phone_valid\\\":\\\"valid\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"email_status\\\":\\\"undeliverable\\\",\\\"email_domain\\\":\\\"ffid.tech\\\",\\\"similar_name\\\":true,\\\"score\\\":3.9,\\\"page\\\":\\\"https:\\\\\\/\\\\\\/lead.ffid.io?l=66ff138d1cbceb7bc46fb1e5\\\"},\\\"webhook_send\\\":false,\\\"webhook_external_id\\\":\\\"\\\",\\\"webhook_integration\\\":\\\"\\\",\\\"temperature\\\":\\\"cold\\\",\\\"ai_agent\\\":false,\\\"open\\\":[],\\\"data\\\":{\\\"custom_origin\\\":\\\"\\\",\\\"custom_origin_name\\\":\\\"\\\"},\\\"extra_info\\\":{\\\"regional\\\":\\\"SP\\\",\\\"answers-2-pergunta\\\":\\\"comprar\\\",\\\"validate_code\\\":526173,\\\"lead_summary\\\":\\\"Cliente: Customer\\\\nFAC: 1727992717\\\\nEMPREENDIMENTO: Soul Miguel Yunes\\\\nIn\\\\u00edcio da conversa: 02\\\\\\/10\\\\\\/24 16:56\\\\n\\\\nPOR ORDEM DE PRIORIDADE, O ATENDIMENTO SEGUIU POR:\\\\n\\\\n\\\\u2022\\\\u2060  \\\\u2060Tipos de apartamentos (tipologia):\\\\nCliente perguntou sobre:\\\\n- Soul Miguel Yunes\\\\n\\\\n\\\\u2022\\\\u2060  \\\\u2060Localiza\\\\u00e7\\\\u00e3o: \\\\nCliente perguntou sobre:\\\\n- Proximidade da esta\\\\u00e7\\\\u00e3o de metr\\\\u00f4\\\\n\\\\n\\\\u2022\\\\u2060  \\\\u2060\\\\u00c1rea de lazer:\\\\nCliente perguntou sobre:\\\\n- Piscina, churrasqueira, academia\\\\n\\\\nCliente tamb\\\\u00e9m perguntou sobre:\\\\n- Se aceita FGTS\\\\n\\\\nPrincipal d\\\\u00favida:\\\\n\\\\n\\\\u2022\\\\u2060  \\\\u2060A pergunta que direcionou o cliente para o seu atendimento foi:\\\\n- Fica perto de qual esta\\\\u00e7\\\\u00e3o de metr\\\\u00f4?\\\\n\\\\nDemais assuntos:\\\\n\\\\n\\\\u2022\\\\u2060  \\\\u2060Sinalizou sobre visita: N\\\\u00e3o perguntou\\\\n\\\\u2022\\\\u2060  \\\\u2060Sinalizou sobre metr\\\\u00f4 e dist\\\\u00e2ncia: N\\\\u00e3o perguntou\\\\n\\\\n*Revis\\\\u00e3o Final*: Todas as informa\\\\u00e7\\\\u00f5es relevantes foram capturadas e o relat\\\\u00f3rio est\\\\u00e1 completo e consistente.\\\"},\\\"responsible_user\\\":\\\"\\\",\\\"status\\\":\\\"new\\\",\\\"ai_chat_resume\\\":\\\"Cliente: Customer\\\\nFAC: 1727992717\\\\nEMPREENDIMENTO: Soul Miguel Yunes\\\\nIn\\\\u00edcio da conversa: 02\\\\\\/10\\\\\\/24 16:56\\\\n\\\\nPOR ORDEM DE PRIORIDADE, O ATENDIMENTO SEGUIU POR:\\\\n\\\\n\\\\u2022\\\\u2060  \\\\u2060Tipos de apartamentos (tipologia):\\\\nCliente perguntou sobre:\\\\n- Soul Miguel Yunes\\\\n\\\\n\\\\u2022\\\\u2060  \\\\u2060Localiza\\\\u00e7\\\\u00e3o: \\\\nCliente perguntou sobre:\\\\n- Proximidade da esta\\\\u00e7\\\\u00e3o de metr\\\\u00f4\\\\n\\\\n\\\\u2022\\\\u2060  \\\\u2060\\\\u00c1rea de lazer:\\\\nCliente perguntou sobre:\\\\n- Piscina, churrasqueira, academia\\\\n\\\\nCliente tamb\\\\u00e9m perguntou sobre:\\\\n- Se aceita FGTS\\\\n\\\\nPrincipal d\\\\u00favida:\\\\n\\\\n\\\\u2022\\\\u2060  \\\\u2060A pergunta que direcionou o cliente para o seu atendimento foi:\\\\n- Fica perto de qual esta\\\\u00e7\\\\u00e3o de metr\\\\u00f4?\\\\n\\\\nDemais assuntos:\\\\n\\\\n\\\\u2022\\\\u2060  \\\\u2060Sinalizou sobre visita: N\\\\u00e3o perguntou\\\\n\\\\u2022\\\\u2060  \\\\u2060Sinalizou sobre metr\\\\u00f4 e dist\\\\u00e2ncia: N\\\\u00e3o perguntou\\\\n\\\\n*Revis\\\\u00e3o Final*: Todas as informa\\\\u00e7\\\\u00f5es relevantes foram capturadas e o relat\\\\u00f3rio est\\\\u00e1 completo e consistente.\\\",\\\"created_at\\\":\\\"2024-10-03 21:58:37\\\",\\\"updated_at\\\":\\\"2024-10-03 21:58:37\\\",\\\"id\\\":\\\"66ff138d1cbceb7bc46fb1e5\\\"}\",\"object_customer\":\"{\\\"id\\\":\\\"669182b102d2bf76d86223c8\\\",\\\"first_name\\\":\\\"Carol \\\",\\\"last_name\\\":\\\"Batista\\\",\\\"birthdate\\\":\\\"\\\",\\\"gender\\\":\\\"\\\",\\\"emails\\\":[{\\\"address\\\":\\\"<EMAIL>\\\",\\\"bounced\\\":0,\\\"active\\\":true}],\\\"phones\\\":[\\\"+5521967622683\\\"],\\\"identification\\\":[],\\\"address\\\":[],\\\"personal\\\":[],\\\"integrations\\\":{\\\"whatsapp\\\":true},\\\"interactions\\\":{\\\"last_seen\\\":false,\\\"avatar\\\":\\\"https:\\\\\\/\\\\\\/cdn.fulfilling.io\\\\\\/template\\\\\\/media\\\\\\/5e4e8ec7f7473d775e1ab43b\\\\\\/95b9ee6c9608f2c58b19eb048e497392.jpg\\\"},\\\"score\\\":3.9,\\\"created_at\\\":\\\"2024-07-12 16:23:29\\\",\\\"updated_at\\\":\\\"2024-09-09 16:37:26\\\",\\\"full_name\\\":\\\"Carol  Batista\\\",\\\"tags\\\":[],\\\"custom\\\":[],\\\"avatar\\\":\\\"https:\\\\\\/\\\\\\/cdn.fulfilling.io\\\\\\/template\\\\\\/media\\\\\\/5e4e8ec7f7473d775e1ab43b\\\\\\/95b9ee6c9608f2c58b19eb048e497392.jpg\\\",\\\"search_data_value\\\":\\\"<EMAIL>\\\",\\\"search_data_type\\\":\\\"email\\\",\\\"active\\\":1}\",\"key\":\"4097cfbe9811aead2c7d44df9524b908aAf9cab6\"}\n\n//Lead Carol 2024-10-23\n// {\"lead_id\":\"test-will-sdsd00\",\"produto_id\":\"289\",\"produto_nome\":\"Cidade Lapa - Santa Marina\",\"object_lead\":\"{\\\"client_id\\\":\\\"5e4e8ec7f7473d775e1ab43b\\\",\\\"customer_id\\\":\\\"669182b102d2bf76d86223c8\\\",\\\"lead_id\\\":\\\"1729703638\\\",\\\"product_id\\\":\\\"289\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"phone\\\":\\\"+5521967622683\\\",\\\"score\\\":3.9,\\\"font\\\":\\\"api\\\",\\\"origin\\\":\\\"whatsapp\\\",\\\"score_data\\\":{\\\"e164\\\":\\\"+5521967622683\\\",\\\"formatted_phone\\\":\\\"(21) 96762-2683\\\",\\\"whatsapp\\\":true,\\\"last_seen\\\":0,\\\"avatar\\\":\\\"https:\\\\\\/\\\\\\/cdn.fulfilling.io\\\\\\/template\\\\\\/media\\\\\\/5e4e8ec7f7473d775e1ab43b\\\\\\/76281ed4befdc729088da8a303182b34.jpg\\\",\\\"phone_type\\\":\\\"MOBILE\\\",\\\"wa_name\\\":\\\"Carol Batista\\\",\\\"phone_valid\\\":\\\"valid\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"email_status\\\":\\\"undeliverable\\\",\\\"email_domain\\\":\\\"ffid.tech\\\",\\\"similar_name\\\":true,\\\"score\\\":3.9,\\\"page\\\":\\\"https:\\\\\\/\\\\\\/lead.ffid.io?l=67192ed605a0bc47bb515be5\\\"},\\\"webhook_send\\\":false,\\\"webhook_external_id\\\":\\\"\\\",\\\"webhook_integration\\\":\\\"\\\",\\\"temperature\\\":\\\"warm\\\",\\\"ai_agent\\\":true,\\\"open\\\":[],\\\"data\\\":{\\\"pid\\\":\\\"289\\\",\\\"extra_info\\\":{\\\"answers-2-pergunta\\\":\\\"Comprar\\\",\\\"answers-3-comprar-apartamento-regional\\\":\\\"SP\\\"},\\\"code\\\":\\\"289\\\",\\\"product_url\\\":\\\"Cidade Lapa - Santa Marina\\\",\\\"custom_origin\\\":\\\"\\\",\\\"custom_origin_name\\\":\\\"\\\"},\\\"extra_info\\\":{\\\"validate_code\\\":777174,\\\"answers-2-pergunta\\\":\\\"Comprar\\\",\\\"answers-3-comprar-apartamento-regional\\\":\\\"SP\\\",\\\"answers_2_pergunta\\\":\\\"Comprar\\\",\\\"answers_3_comprar_apartamento_regional\\\":\\\"SP\\\"},\\\"responsible_user\\\":\\\"\\\",\\\"status\\\":\\\"new\\\",\\\"ai_chat_resume\\\":\\\"Cliente: Carol Batista\\\\nFAC: 1729703638\\\\nEMPREENDIMENTO: Cidade Lapa - Santa Marina\\\\nIn\\\\u00edcio da conversa: 23\\\\\\/10\\\\\\/24 13:54\\\\n\\\\nPOR ORDEM DE PRIORIDADE, O ATENDIMENTO SEGUIU POR:\\\\n\\\\n\\\\u2022\\\\u2060  \\\\u2060Tipos de apartamentos (tipologia):\\\\nCliente perguntou sobre:\\\\n- Im\\\\u00f3veis na Zona Sul de S\\\\u00e3o Paulo\\\\n- Im\\\\u00f3veis na Vila Mascote\\\\n\\\\n\\\\u2022\\\\u2060  \\\\u2060Localiza\\\\u00e7\\\\u00e3o: \\\\nCliente perguntou sobre:\\\\n- Im\\\\u00f3veis na Zona Sul de S\\\\u00e3o Paulo\\\\n- Im\\\\u00f3veis na Vila Mascote\\\\n- Im\\\\u00f3vel na Lapa\\\\n\\\\n\\\\u2022\\\\u2060  \\\\u2060\\\\u00c1rea de lazer:\\\\nCliente perguntou sobre:\\\\n- Comodidades do empreendimento Cidade Lapa - Santa Marina\\\\n\\\\nCliente tamb\\\\u00e9m perguntou sobre:\\\\n- Im\\\\u00f3veis na Zona Sul de S\\\\u00e3o Paulo\\\\n- Im\\\\u00f3veis na Vila Mascote\\\\n\\\\nPrincipal d\\\\u00favida:\\\\n\\\\n\\\\u2022\\\\u2060  \\\\u2060A pergunta que direcionou o cliente para o seu atendimento foi:\\\\n- Exist\\\\u00eancia de im\\\\u00f3vel na Lapa\\\\n\\\\nDemais assuntos:\\\\n\\\\n\\\\u2022\\\\u2060  \\\\u2060sinalizou sobre visita: N\\\\u00e3o perguntou\\\\n\\\\u2022\\\\u2060  \\\\u2060sinalizou sobre metr\\\\u00f4 e dist\\\\u00e2ncia: N\\\\u00e3o perguntou\\\\n\\\\n*Revis\\\\u00e3o Final*: Todas as perguntas feitas pelo cliente foram devidamente classificadas, e o relat\\\\u00f3rio est\\\\u00e1 completo e consistente.\\\",\\\"created_at\\\":\\\"2024-10-23 17:13:58\\\",\\\"updated_at\\\":\\\"2024-10-23 17:13:58\\\",\\\"id\\\":\\\"67192ed605a0bc47bb515be5\\\"}\",\"object_customer\":\"{\\\"id\\\":\\\"669182b102d2bf76d86223c8\\\",\\\"first_name\\\":\\\"Carol \\\",\\\"last_name\\\":\\\"Batista\\\",\\\"birthdate\\\":\\\"\\\",\\\"gender\\\":\\\"\\\",\\\"emails\\\":[{\\\"address\\\":\\\"<EMAIL>\\\",\\\"bounced\\\":0,\\\"active\\\":true}],\\\"phones\\\":[\\\"+5521967622683\\\"],\\\"identification\\\":[],\\\"address\\\":[],\\\"personal\\\":[],\\\"integrations\\\":{\\\"whatsapp\\\":true},\\\"interactions\\\":{\\\"last_seen\\\":false,\\\"avatar\\\":\\\"https:\\\\\\/\\\\\\/cdn.fulfilling.io\\\\\\/template\\\\\\/media\\\\\\/5e4e8ec7f7473d775e1ab43b\\\\\\/76281ed4befdc729088da8a303182b34.jpg\\\"},\\\"score\\\":3.9,\\\"created_at\\\":\\\"2024-07-12 16:23:29\\\",\\\"updated_at\\\":\\\"2024-10-11 16:50:04\\\",\\\"full_name\\\":\\\"Carol  Batista\\\",\\\"tags\\\":[\\\"64b7e4a4e4a2eb5871190848\\\",\\\"667c1cc5ac8bed30937aad43\\\",\\\"637cc1af29bd25d5c309bf60\\\",\\\"66e48e090dafcd1241764ff3\\\",\\\"66f5b9810087046263133dd3\\\"],\\\"custom\\\":[],\\\"avatar\\\":\\\"https:\\\\\\/\\\\\\/cdn.fulfilling.io\\\\\\/template\\\\\\/media\\\\\\/5e4e8ec7f7473d775e1ab43b\\\\\\/76281ed4befdc729088da8a303182b34.jpg\\\",\\\"search_data_value\\\":\\\"<EMAIL>\\\",\\\"search_data_type\\\":\\\"email\\\",\\\"active\\\":1}\",\"key\":\"4097cfbe9811aead2c7d44df9524b908aAf9cab6\"}\n\n// Lead Millene - 2024-10-24\n{\"lead_id\":\"test-millene-1729795856-b\",\"produto_id\":\"282\",\"produto_nome\":\"Completo Parque Brito III\",\"object_lead\":\"{\\\"client_id\\\":\\\"5e4e8ec7f7473d775e1ab43b\\\",\\\"customer_id\\\":\\\"66f1b02c21d3fe6891261d42\\\",\\\"lead_id\\\":\\\"1729795856\\\",\\\"product_id\\\":\\\"282\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"phone\\\":\\\"+5511942534639\\\",\\\"score\\\":5,\\\"font\\\":\\\"api\\\",\\\"origin\\\":\\\"whatsapp\\\",\\\"score_data\\\":{\\\"e164\\\":\\\"+5511942534639\\\",\\\"formatted_phone\\\":\\\"(11) 94253-4639\\\",\\\"whatsapp\\\":true,\\\"last_seen\\\":0,\\\"avatar\\\":\\\"https:\\\\\\/\\\\\\/cdn.fulfilling.io\\\\\\/template\\\\\\/media\\\\\\/5e4e8ec7f7473d775e1ab43b\\\\\\/2f0d3a600280322a0297f5f2d68c9429.jpg\\\",\\\"phone_type\\\":\\\"MOBILE\\\",\\\"wa_name\\\":\\\"Millene Carvalho\\\",\\\"phone_valid\\\":\\\"valid\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"email_status\\\":\\\"deliverable\\\",\\\"email_domain\\\":\\\"gmail.com\\\",\\\"similar_name\\\":true,\\\"score\\\":5,\\\"page\\\":\\\"https:\\\\\\/\\\\\\/lead.ffid.io?l=671a97102fb2620d00736fd5\\\"},\\\"webhook_send\\\":false,\\\"webhook_external_id\\\":\\\"\\\",\\\"webhook_integration\\\":\\\"\\\",\\\"temperature\\\":\\\"hot\\\",\\\"ai_agent\\\":true,\\\"open\\\":[],\\\"data\\\":{\\\"pid\\\":\\\"282\\\",\\\"extra_info\\\":{\\\"answers-2-pergunta\\\":\\\"Comprar\\\",\\\"answers-3-comprar-apartamento-regional\\\":\\\"SP\\\"},\\\"code\\\":\\\"282\\\",\\\"product_url\\\":\\\"Completo Parque Brito III\\\",\\\"custom_origin\\\":\\\"\\\",\\\"custom_origin_name\\\":\\\"\\\"},\\\"extra_info\\\":{\\\"validate_code\\\":139711,\\\"answers-2-pergunta\\\":\\\"Comprar\\\",\\\"answers-3-comprar-apartamento-regional\\\":\\\"SP\\\",\\\"answers_2_pergunta\\\":\\\"Comprar\\\",\\\"answers_3_comprar_apartamento_regional\\\":\\\"SP\\\"},\\\"responsible_user\\\":\\\"\\\",\\\"status\\\":\\\"new\\\",\\\"ai_chat_resume\\\":\\\"Cliente: Millene Carvalho\\\\nFAC: 1729795856\\\\nEMPREENDIMENTO: Completo Parque Brito III\\\\nIn\\\\u00edcio da conversa: 24\\\\\\/10\\\\\\/24 15:42\\\\n\\\\nPOR ORDEM DE PRIORIDADE, O ATENDIMENTO SEGUIU POR:\\\\n\\\\n\\\\u2022 Tipologia:\\\\nCliente perguntou sobre:\\\\n- Nem todas as unidades do Completo Parque Brito III possuem vaga de garagem.\\\\n\\\\n\\\\u2022 Localiza\\\\u00e7\\\\u00e3o:\\\\nCliente perguntou sobre:\\\\n- Proximidade de outros empreendimentos da Cury em Campo Grande.\\\\n\\\\n\\\\u2022 D\\\\u00favidas financeiras:\\\\nCliente perguntou sobre:\\\\n- Compra sem financiamento\\\\n- Financiamento pela Caixa Econ\\\\u00f4mica Federal\\\\n\\\\nCliente tamb\\\\u00e9m perguntou sobre:\\\\n- Como funciona a compra de um im\\\\u00f3vel\\\\n\\\\nPrincipal d\\\\u00favida:\\\\n\\\\n\\\\u2022 A pergunta que direcionou o cliente para o seu atendimento foi:\\\\nComo fa\\\\u00e7o para comprar?\\\\n\\\\nDemais assuntos:\\\\n\\\\n\\\\u2022 Sinalizou sobre visita: N\\\\u00e3o perguntou\\\\n\\\\u2022 Sinalizou sobre metr\\\\u00f4 e dist\\\\u00e2ncia: N\\\\u00e3o perguntou\\\\n\\\\n*Revis\\\\u00e3o Final*: Todas as perguntas feitas pelo cliente foram devidamente classificadas, e o relat\\\\u00f3rio est\\\\u00e1 completo e consistente.\\\",\\\"created_at\\\":\\\"2024-10-24 18:50:56\\\",\\\"updated_at\\\":\\\"2024-10-24 18:50:56\\\",\\\"id\\\":\\\"671a97102fb2620d00736fd5\\\"}\",\"object_customer\":\"{\\\"id\\\":\\\"66f1b02c21d3fe6891261d42\\\",\\\"first_name\\\":\\\"Millene\\\",\\\"last_name\\\":\\\"Carvalho\\\",\\\"birthdate\\\":\\\"\\\",\\\"gender\\\":\\\"\\\",\\\"emails\\\":[{\\\"address\\\":\\\"<EMAIL>\\\",\\\"bounced\\\":0,\\\"active\\\":true}],\\\"phones\\\":[\\\"+5511942534639\\\"],\\\"identification\\\":[],\\\"address\\\":[],\\\"personal\\\":[],\\\"integrations\\\":{\\\"whatsapp\\\":true},\\\"interactions\\\":{\\\"last_seen\\\":false,\\\"avatar\\\":\\\"https:\\\\\\/\\\\\\/cdn.fulfilling.io\\\\\\/template\\\\\\/media\\\\\\/5e4e8ec7f7473d775e1ab43b\\\\\\/2f0d3a600280322a0297f5f2d68c9429.jpg\\\"},\\\"score\\\":5,\\\"created_at\\\":\\\"2024-09-23 15:15:08\\\",\\\"updated_at\\\":\\\"2024-10-23 18:06:01\\\",\\\"full_name\\\":\\\"Millene Carvalho\\\",\\\"tags\\\":[\\\"5eb1c1f1ad4ab81cc144e5b4\\\",\\\"64ef81cc65cd4c178a55446e\\\",\\\"64b7e4a4e4a2eb5871190848\\\",\\\"66ec802d1823de0445115e23\\\",\\\"637cbfdb25e0095d4708e944\\\",\\\"6697d43dda3f736f9206a583\\\",\\\"669830d1d7709f307b63a093\\\"],\\\"custom\\\":[],\\\"avatar\\\":\\\"https:\\\\\\/\\\\\\/cdn.fulfilling.io\\\\\\/template\\\\\\/media\\\\\\/5e4e8ec7f7473d775e1ab43b\\\\\\/2f0d3a600280322a0297f5f2d68c9429.jpg\\\",\\\"search_data_value\\\":\\\"<EMAIL>\\\",\\\"search_data_type\\\":\\\"email\\\",\\\"active\\\":1}\",\"key\":\"4097cfbe9811aead2c7d44df9524b908aAf9cab6\"}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/webhooks/ffid/lead", "host": ["{{url}}"], "path": ["webhooks", "ffid", "lead"]}}, "response": []}]}, {"name": "CuryBank", "item": [{"name": "Transfers", "item": [{"name": "PIX", "item": [{"name": "2.6.5. <PERSON><PERSON>", "item": [{"name": "Listar chaves PIX", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{url}}/app/bank-account/transfer/pix/keys?pix_key_status=active", "host": ["{{url}}"], "path": ["app", "bank-account", "transfer", "pix", "keys"], "query": [{"key": "pix_key_status", "value": "active"}]}, "description": "<PERSON>a todas as chaves PIX de uma conta específica"}, "response": []}, {"name": "Criar chave PIX", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"pix_key_type\": \"cpf\",\n    \"pix_key\": \"495.833.730-01\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/app/bank-account/transfer/pix/keys", "host": ["{{url}}"], "path": ["app", "bank-account", "transfer", "pix", "keys"]}, "description": "Cria uma nova chave PIX. Tipos disponíveis: cpf, cnpj, email, phone, random"}, "response": []}, {"name": "Consultar chave PIX", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{url}}/app/bank-account/transfer/pix/keys/:key", "host": ["{{url}}"], "path": ["app", "bank-account", "transfer", "pix", "keys", ":key"], "variable": [{"key": "key", "value": "495.833.730-01"}]}, "description": "Consulta informações de uma chave PIX específica"}, "response": []}, {"name": "Excluir chave PIX", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{url}}/app/bank-account/transfer/pix/keys/:key", "host": ["{{url}}"], "path": ["app", "bank-account", "transfer", "pix", "keys", ":key"], "variable": [{"key": "key", "value": "***********"}]}, "description": "Exclui uma chave PIX da conta"}, "response": []}, {"name": "Aprovação chave PIX", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"verification_code\": \"756816\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/app/bank-account/transfer/pix/keys/:pixKeyRequestKey/twofa_validation", "host": ["{{url}}"], "path": ["app", "bank-account", "transfer", "pix", "keys", ":pixKeyRequestKey", "twofa_validation"], "variable": [{"key": "pixKeyRequestKey", "value": "df258ecf-1a79-4ae8-b6ee-e179d894263a"}]}, "description": "Exclui uma chave PIX da conta"}, "response": []}], "description": "Gerenciamento de chaves PIX conforme documentação QiTech 2.6.5"}, {"name": "2.6.2. Movimentaç<PERSON><PERSON>", "item": [{"name": "2fa", "item": [{"name": "2fa Token transferência PIX", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{url}}/app/bank-account/transfer/pix/2fa/token/:transactionRequestKey", "host": ["{{url}}"], "path": ["app", "bank-account", "transfer", "pix", "2fa", "token", ":transactionRequestKey"], "variable": [{"key": "transactionRequestKey", "value": "2b1441f7-baee-41e6-9533-1d52393a4b11"}]}, "description": "Consulta detalhes de uma transferência PIX específica"}, "response": []}, {"name": "Aprova transferência PIX", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"token\": \"329329\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/app/bank-account/transfer/pix/2fa/validate-token/:pixTransferKey", "host": ["{{url}}"], "path": ["app", "bank-account", "transfer", "pix", "2fa", "validate-token", ":pixTransferKey"], "variable": [{"key": "pixTransferKey", "value": "2b1441f7-baee-41e6-9533-1d52393a4b11"}]}, "description": "Consulta detalhes de uma transferência PIX específica"}, "response": []}]}, {"name": "Listar transferências PIX", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{url}}/app/bank-account/transfer/pix/transfers?account_key={{account_key}}&start_date=2024-01-01&end_date=2024-12-31&status=completed&page=1&limit=50", "host": ["{{url}}"], "path": ["app", "bank-account", "transfer", "pix", "transfers"], "query": [{"key": "account_key", "value": "{{account_key}}"}, {"key": "start_date", "value": "2024-01-01"}, {"key": "end_date", "value": "2024-12-31"}, {"key": "status", "value": "completed"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "50"}]}, "description": "Lista transferências PIX com filtros opcionais"}, "response": []}, {"name": "Realizar transferência PIX", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "// //cpf\n{\n    \"pix_transfer_type\": \"key\",\n    \"target_pix_key\": \"***********\",\n    \"transaction_amount\": 5.00,\n    \"pix_message\": \"<PERSON>la Mundo\",\n    \"tfa_info\": {\n        \"approver_document_number\": \"***********\",\n        \"contact_type\": \"sms\"\n    }\n}\n\n// para toda transacao precisar consultar novamente o qrcode pra  pegar o end_to_end_id\n//qrcode\n// {\n//   \"pix_transfer_type\": \"static_qr_code\",\n//   \"target_pix_key\": \"+*************\",\n//   \"transaction_amount\": 500.65,\n//   \"end_to_end_id\": \"E32402502202507311722TUSJZH0f68E\",\n//   \"pix_message\": \"Ola Mundo\",\n//   \"tfa_info\": {\n//     \"approver_document_number\": \"***********\",\n//     \"contact_type\": \"email\"\n//   }  \n// }\n// MANUAL - agencia e conta\n// {\n//     \"pix_transfer_type\": \"manual\",\n//     \"target_account\": {\n//         \"account_branch\": \"104\",\n//         \"account_digit\": \"5\",\n//         \"account_number\": \"21837\",\n//         \"owner_document_number\": \"***********\",\n//         \"owner_name\": \"Mock Person Name\",\n//         \"account_type\": \"payment_account\",\n//         \"ispb\": \"********\"\n//     },\n//     \"transaction_amount\": 500.65,\n//     \"pix_message\": \"Ola Mundo\",\n//     \"tfa_info\": {\n//         \"approver_document_number\": \"***********\",\n//         \"contact_type\": \"email\"\n//     }\n// }", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/app/bank-account/transfer/pix/transfers", "host": ["{{url}}"], "path": ["app", "bank-account", "transfer", "pix", "transfers"]}, "description": "Realiza uma transferência PIX para uma chave específica"}, "response": []}, {"name": "Consultar transferência PIX", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{url}}/app/bank-account/transfer/pix/transfers/:transferId/:direction", "host": ["{{url}}"], "path": ["app", "bank-account", "transfer", "pix", "transfers", ":transferId", ":direction"], "variable": [{"key": "transferId", "value": "2b1441f7-baee-41e6-9533-1d52393a4b11"}, {"key": "direction", "value": "outgoing"}]}, "description": "Consulta detalhes de uma transferência PIX específica"}, "response": []}], "description": "Transferências PIX conforme documentação QiTech 2.6.2"}, {"name": "2.6.2. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item": [{"name": "Solicitar devolução PIX", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"request_control_key\": \"303393bf-8f2e-4ff0-b326-ee7ad612e8ca\",\n  \"reversal_amount\": 147,\n  \"reversal_reason\": \"client_request\",\n  \"reversal_message\": \"Mensagem Pix da Devolução\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/app/bank-account/transfer/pix/refunds", "host": ["{{url}}"], "path": ["app", "bank-account", "transfer", "pix", "refunds"]}, "description": "Solicita devolução total ou parcial de um PIX recebido"}, "response": []}], "description": "Devoluções PIX conforme documentação QiTech 2.6.2"}, {"name": "2.6.6. QR Code", "item": [{"name": "Decodificar QR Code PIX", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"qr_code_payload\": \"00020126360014BR.GOV.BCB.PIX0114+*************5204000053039865406100.005802BR5912teste de pix6009Sao Paulo62070503***********\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/app/bank-account/transfer/pix/qr-codes/decode", "host": ["{{url}}"], "path": ["app", "bank-account", "transfer", "pix", "qr-codes", "decode"]}, "description": "Decodifica um QR Code PIX e retorna suas informações"}, "response": []}, {"name": "Criar QR Code PIX estático", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"qr_code_format\": \"both\",\n    \"pix_key\": \"***********\",\n    \"amount\": 10.25\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/app/bank-account/transfer/pix/qr-codes/static", "host": ["{{url}}"], "path": ["app", "bank-account", "transfer", "pix", "qr-codes", "static"]}, "description": "Cria um QR Code PIX estático com valor fixo ou aberto"}, "response": []}, {"name": "Criar QR Code PIX dinâmico", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"account_key\": \"f0d363be-fc49-4cfc-a1f8-c8d4d4195095\",\n  \"amount\": 22.34,\n  \"occurrence_type\": \"registration\",\n  \"payer_document_number\": \"**************\",\n  \"payer_name\": \"Random\",\n  \"payer_person_type\": \"legal\",\n  \"payer_request\": \"Payment for order XXXXXXXXXX\",\n  \"pix_key\": \"3d7d6a2b-f72f-44z7-bb20-79a94dff5645\",\n  \"receiver_conciliation_id\": \"3d7d6a2bf72f44z7bb2079a94dff5645\",\n  \"qr_code_type\": \"dynamic_term\",\n  \"additional_data\": [\n    {\n      \"key_name\": \"Juros e Multa\",\n      \"value\": \"Juros 2 ao mes e multa de 1%\"\n    }\n  ],\n  \"fine_amount\": 3,\n  \"interest_amount\": 2,\n  \"expiration_date\": \"2023-03-25\",\n  \"max_payment_days\": 128,\n  \"rebate_amount\": 1,\n  \"discounts\": []\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/app/bank-account/transfer/pix/qr-codes/dynamic", "host": ["{{url}}"], "path": ["app", "bank-account", "transfer", "pix", "qr-codes", "dynamic"]}, "description": "Cria um QR Code PIX dinâmico com valor específico e prazo de validade"}, "response": []}, {"name": "Baixar QR Code PIX dinâmico", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{url}}/app/bank-account/transfer/pix/qr-codes/dynamic/:qrCodeId/download?format=png&size=300", "host": ["{{url}}"], "path": ["app", "bank-account", "transfer", "pix", "qr-codes", "dynamic", ":qrCodeId", "download"], "query": [{"key": "format", "value": "png"}, {"key": "size", "value": "300"}], "variable": [{"key": "qrCodeId", "value": "{{qr_code_id}}"}]}, "description": "Baixa a imagem de um QR Code PIX dinâmico"}, "response": []}], "description": "QR Codes PIX conforme documentação QiTech 2.6.6"}, {"name": "2.6.7. <PERSON><PERSON><PERSON><PERSON><PERSON>", "item": [{"name": "Obter comprovante de transferência", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{url}}/app/bank-account/transfer/pix/transfers/:transferId/receipt", "host": ["{{url}}"], "path": ["app", "bank-account", "transfer", "pix", "transfers", ":transferId", "receipt"], "variable": [{"key": "transferId", "value": ""}]}, "description": "Obtém o comprovante de uma transferência PIX específica"}, "response": []}, {"name": "Obter transferência com comprovante completo", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{url}}/app/bank-account/transfer/pix/transfers/:transferId/receipt/full", "host": ["{{url}}"], "path": ["app", "bank-account", "transfer", "pix", "transfers", ":transferId", "receipt", "full"], "variable": [{"key": "transferId", "value": ""}]}, "description": "Obtém dados completos da transferência incluindo comprovante detalhado"}, "response": []}], "description": "Comprovantes PIX conforme documentação QiTech 2.6.7"}], "description": "Endpoints PIX reorganizados conforme nova estrutura - QiTech API"}, {"name": "Lista tipos de contas", "request": {"method": "GET", "header": [], "url": {"raw": "{{url}}/app/bank-account/transfers/list-account-types", "host": ["{{url}}"], "path": ["app", "bank-account", "transfers", "list-account-types"]}}, "response": []}, {"name": "Lista Bancos", "request": {"method": "GET", "header": [], "url": {"raw": "{{url}}/app/bank-account/transfers/list-banks", "host": ["{{url}}"], "path": ["app", "bank-account", "transfers", "list-banks"]}}, "response": []}]}, {"name": "Bank Account", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Extract account_key\", function () {", "    var jsonData = pm.response.json();", "    if (jsonData.account_key) {", "        pm.environment.set(\"account_key\", jsonData.account_key);", "        console.log(\"Account key set:\", jsonData.account_key);", "    }", "});", "", "pm.test(\"Response status code is 200\", function () {", "    pm.expect(pm.response.code).to.equal(200);", "});", "", "", "pm.test(\"Response should contain the required field 'status'\", function () {", "    const responseData = pm.response.json();", "    ", "    pm.expect(responseData).to.be.an('object');", "    pm.expect(responseData).to.have.property('status').that.is.a('string');", "});", "", "", "pm.test(\"Response time is less than 200ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(200);", "});", "", "", "pm.test(\"The terms_url is a valid URL when status is 'initial'\", function () {", "    const responseData = pm.response.json();", "    ", "    pm.expect(responseData).to.be.an('object');", "    pm.expect(responseData.status).to.equal(\"initial\");", "    pm.expect(responseData.terms_url).to.exist;", "    pm.expect(responseData.terms_url).to.match(/^https?:\\/\\/[^ \"]+$/, \"terms_url should be a valid URL\");", "});", "", "", "pm.test(\"Response is an object with expected properties for 'terms_accepted' status\", function () {", "    const responseData = pm.response.json();", "", "    pm.expect(responseData).to.be.an('object');", "    pm.expect(responseData).to.have.property('status').that.equals('terms_accepted');", "    ", "    pm.expect(responseData).to.have.property('user').that.is.an('object');", "", "    const user = responseData.user;", "    pm.expect(user).to.have.all.keys('name', 'document_number', 'email', 'cellphone', 'birthdate', 'mother_name', 'is_pep', 'nationality');", "    ", "    pm.expect(user.name).to.be.a('string').and.to.have.length.above(0);", "    pm.expect(user.document_number).to.be.a('string').and.to.match(/^\\d{3}\\.\\d{3}\\.\\d{3}-\\d{2}$/);", "    pm.expect(user.email).to.be.a('string').and.to.match(/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/);", "    pm.expect(user.cellphone).to.be.a('string').and.to.match(/^\\(\\d{2}\\) \\d{5}-\\d{4}$/);", "    pm.expect(user.birthdate).to.be.a('string').and.to.match(/^\\d{4}-\\d{2}-\\d{2}$/);", "    pm.expect(user.mother_name).to.satisfy(value => value === null || typeof value === 'string');", "    pm.expect(user.is_pep).to.satisfy(value => value === null || typeof value === 'boolean');", "    pm.expect(user.nationality).to.satisfy(value => value === null || typeof value === 'string');", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{url}}/app/bank-account/transfers//ist-account-types", "host": ["{{url}}"], "path": ["app", "bank-account", "transfers", "", "ist-account-types"]}, "description": "Esse é o endpoint inicial, chamado ao acessar o cury bank.\n\nO JSON retornado muda de acordo com o status da conta:\n\n- Status `initial`\n    \n\nÉ o endpoint inicial, quando o usuário não realizou nenhuma ação. O usuário deve ser direcionado apra a primeira tela (a do aceite de termos)\n\n``` json\n{\n    \"status\": \"initial\",\n    \"terms_url\": \"http://corretor-cury.localhost:8088/storage/test.pdf\"\n}\n\n ```\n\n- Status `terms_accepted`\n    \n\nO usuário chega nesse status após aceitar os termos de uso. Ele deve ser direcionado para a primeira tela do cadastro de documentos. Nesse endpoint a gente retorna os dados que temos pré-preenchidos do formulário:\n\n``` json\n{\n    \"status\": \"terms_accepted\",\n    \"user\": {\n        \"name\": \"Dr. <PERSON>uza Furtado\",\n        \"document_number\": \"670.020.580-88\",\n        \"email\": \"<EMAIL>\",\n        \"cellphone\": \"(74) 99271-5002\",\n        \"birthdate\": \"1985-11-28\",\n        \"mother_name\": null,\n        \"is_pep\": null,\n        \"nationality\": null,\n        \"monthly_income\": null,\n        \"address\": {\n            \"street\": \"Rua <PERSON>o Sanches\",\n            \"number\": \"29\",\n            \"complement\": null,\n            \"neighborhood\": \"Juliane d'Oeste\",\n            \"city\": \"Kevin do Norte\",\n            \"state\": \"PE\",\n            \"postal_code\": \"69541-774\"\n        }\n    }\n}\n\n ```\n\n- Status `creating_account`, `creating_account_reproved`, `creating_account_analysis`, `account_created`, `waiting_contract`, `waiting_salesforce_contact_id`\n    \n\nTodos esses são status após a gente iniciar a abertura de conta. Provavelmente a gente vai ter telas diferentes para alguns desses casos depois, mas por hora todos esses status vamos direcionar para aquela tela de \"aguarde\", após preencher o formulário.\n\n``` json\n{\n    \"status\": \"creating_account ou outro status\",\n    \"status_info\": {\n        \"type\": \"info\" ou \"error\",\n        \"message\": \"Mensagem aqui...\"\n    }\n}\n\n ```\n\n**Importante:** Quando existir esse \"status_info\", precisa exibir aquele box de status na tela de \"aguarde\" (ver no Figma).\n\nO campo `status_info.type` informa se o box é de informação ou de erro, e o `status_info.type` é o texto que deve ser exbidio.\n\n- Status `account_confirmed`\n    \n\nEsse é o status de quando a conta está criada e pronta para uso. Quando tiver esses status devemos direrecionar o user para a home do Cury Bank. Futuramente esse endpoint irá retornar outras informações.\n\n``` json\n{\n    \"status\": \"account_confirmed\",\n}\n\n ```"}, "response": []}, {"name": "Accept terms", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "latitude", "value": "80", "type": "text"}, {"key": "longitude", "value": "80", "type": "text"}]}, "url": {"raw": "{{url}}/app/bank-account/accept-terms", "host": ["{{url}}"], "path": ["app", "bank-account", "accept-terms"]}, "description": "Endpoint para aceitar os termos de uso. Precisa enviar os parâmetros \"latitude\" e \"longitude\".\n\nEm caso de sucesso, esse endpoint irá retornar status 200.\n\nApós receber o status de sucesso, o app deve chamar o endpoint `[GET] /app/bank-account` para direcionar para o cadastro e trazer os dados do formulário."}, "response": []}, {"name": "Documents upload", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "rg_front", "type": "file", "src": "/home/<USER>/Pictures/2025-05-28_11-32.png", "disabled": true}, {"key": "rg_back", "type": "file", "src": "/home/<USER>/Pictures/2025-05-28_11-32.png", "disabled": true}, {"key": "cnh_single", "value": "", "type": "text", "disabled": true}, {"key": "cnh_front", "value": "", "type": "text", "disabled": true}, {"key": "cnh_back", "value": "", "type": "text", "disabled": true}, {"key": "cnh_digital", "value": "", "type": "text", "disabled": true}, {"key": "selfie", "type": "file", "src": "/home/<USER>/Pictures/2025-05-28_11-32.png"}, {"key": "proof_of_residence", "value": "", "type": "text", "disabled": true}]}, "url": {"raw": "{{url}}/app/bank-account/user/upload", "host": ["{{url}}"], "path": ["app", "bank-account", "user", "upload"]}, "description": "Esse é o endpoint para upload de todos os arquivos (incluindo a selfie no final).\n\nO app deve chamar esse endpoint mandando um arquivo por vez. Os nomes dos campos que a gente espera são esses:\n\nrg_front  \nrg_back\n\ncnh_single\n\ncnh_front  \ncnh_back\n\ncnh_digital (**observação:** apenas para esse tipo o backend espera um arquivo no formato .pdf)\n\nproof_of_residence\n\nselfie\n\nEm caso de sucesso, o endpoint vai retornar status 200 com esse json:\n\n``` json\n{\n    \"type\": \"selfie\",\n    \"file_url\": null,\n    \"file_thumb_url\": null\n}\n\n ```\n\nOs campos \"file_url\" e \"file_thumb_url\" sempre vão retornar null por enquanto. O app não precisa salvar nenhum dado do documento após o upload (porque a gente já vai salvar no banco assim qeu faz o upload)."}, "response": []}, {"name": "Documents OCR", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "document_type", "value": "rg_front_back", "type": "text"}]}, "url": {"raw": "{{url}}/app/bank-account/ocr/documents", "host": ["{{url}}"], "path": ["app", "bank-account", "ocr", "documents"]}, "description": "Esse é o endpoint que será chamado quando o usuário clicar no botão \"Avançar\" após subir todos os documentos da primeira parte do cadastro.\n\nComo o processamento pode demorar, seria legal ter um loading nesse botão.\n\nEsse endpoint deve enviar um campo `document_type`, que será o valor selecionado no dropdown \"Tipo do documento pessoal\". Os valores desse campo podem ser:\n\nrg_front_back  \ncnh_single  \ncnh_front_back  \ncnh_digital\n\nEm caso de sucesso, o endpoint retorna status 200 (e o app deve direcionar o user para o próximo passo do cadastro).\n\n**Importante:** Os casos de erro a gente não mapeou ainda, vamos fazer depois."}, "response": []}, {"name": "<PERSON><PERSON> face recognition", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "{{url}}/app/bank-account/face-recon/selfie", "host": ["{{url}}"], "path": ["app", "bank-account", "face-recon", "selfie"]}, "description": "Endpoint para validar a selfie. Esse endpoint deve ser chamado ao clicar no botão \"Finalizar\" do cadastro.\n\nComo o processamento pode ser lento, seria legal ter um \"loading\" após clicar nesse botão.\n\nEm caso de sucesso, a API vai retornar status 200. Ap<PERSON> isso, o App deve chamar o endpoint de criação da conta (\\`\\[POST\\] /app/bank-account\\`).\n\n**Observação:** Por enquanto a gente não tem mapeado os casos de erro na validação, vamos fazer depois."}, "response": []}, {"name": "Create account", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "birthdate", "value": "2000-07-15", "type": "text"}, {"key": "mother_name", "value": "<PERSON><PERSON>", "type": "text"}, {"key": "is_pep", "value": "0", "type": "text"}, {"key": "nationality", "value": "Brasileiro", "type": "text"}, {"key": "monthly_income", "value": "5725.00", "type": "text"}, {"key": "address[street]", "value": "<PERSON><PERSON>", "type": "text"}, {"key": "address[number]", "value": "123", "type": "text"}, {"key": "address[complement]", "value": "(opcional)", "type": "text"}, {"key": "address[neighborhood]", "value": "Nome do Bairro", "type": "text"}, {"key": "address[city]", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "text"}, {"key": "address[state]", "value": "SP", "type": "text"}, {"key": "address[postal_code]", "value": "07015-789", "type": "text"}]}, "url": {"raw": "{{url}}/app/bank-account", "host": ["{{url}}"], "path": ["app", "bank-account"]}, "description": "Endpoint para criação da conta. Verificar payload de exemplo no Postman.\n\nEm caso de sucesso, retorna status 200.\n\n<PERSON>esse momento o App precisa direcionar o usuário para a página de \"Aguarde\" no final do cadastro."}, "response": []}, {"name": "pix", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"pix_key\": \"+************* \"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/app/bank-account/pix/keys/query", "host": ["{{url}}"], "path": ["app", "bank-account", "pix", "keys", "query"]}, "description": "Esse é o endpoint inicial, chamado ao acessar o cury bank.\n\nO JSON retornado muda de acordo com o status da conta:\n\n- Status `initial`\n    \n\nÉ o endpoint inicial, quando o usuário não realizou nenhuma ação. O usuário deve ser direcionado apra a primeira tela (a do aceite de termos)\n\n``` json\n{\n    \"status\": \"initial\",\n    \"terms_url\": \"http://corretor-cury.localhost:8088/storage/test.pdf\"\n}\n\n ```\n\n- Status `terms_accepted`\n    \n\nO usuário chega nesse status após aceitar os termos de uso. Ele deve ser direcionado para a primeira tela do cadastro de documentos. Nesse endpoint a gente retorna os dados que temos pré-preenchidos do formulário:\n\n``` json\n{\n    \"status\": \"terms_accepted\",\n    \"user\": {\n        \"name\": \"Dr. <PERSON>uza Furtado\",\n        \"document_number\": \"670.020.580-88\",\n        \"email\": \"<EMAIL>\",\n        \"cellphone\": \"(74) 99271-5002\",\n        \"birthdate\": \"1985-11-28\",\n        \"mother_name\": null,\n        \"is_pep\": null,\n        \"nationality\": null,\n        \"monthly_income\": null,\n        \"address\": {\n            \"street\": \"Rua <PERSON>o Sanches\",\n            \"number\": \"29\",\n            \"complement\": null,\n            \"neighborhood\": \"Juliane d'Oeste\",\n            \"city\": \"Kevin do Norte\",\n            \"state\": \"PE\",\n            \"postal_code\": \"69541-774\"\n        }\n    }\n}\n\n ```\n\n- Status `creating_account`, `creating_account_reproved`, `creating_account_analysis`, `account_created`, `waiting_contract`, `waiting_salesforce_contact_id`\n    \n\nTodos esses são status após a gente iniciar a abertura de conta. Provavelmente a gente vai ter telas diferentes para alguns desses casos depois, mas por hora todos esses status vamos direcionar para aquela tela de \"aguarde\", após preencher o formulário.\n\n``` json\n{\n    \"status\": \"creating_account ou outro status\",\n    \"status_info\": {\n        \"type\": \"info\" ou \"error\",\n        \"message\": \"Mensagem aqui...\"\n    }\n}\n\n ```\n\n**Importante:** Quando existir esse \"status_info\", precisa exibir aquele box de status na tela de \"aguarde\" (ver no Figma).\n\nO campo `status_info.type` informa se o box é de informação ou de erro, e o `status_info.type` é o texto que deve ser exbidio.\n\n- Status `account_confirmed`\n    \n\nEsse é o status de quando a conta está criada e pronta para uso. Quando tiver esses status devemos direrecionar o user para a home do Cury Bank. Futuramente esse endpoint irá retornar outras informações.\n\n``` json\n{\n    \"status\": \"account_confirmed\",\n}\n\n ```"}, "response": []}, {"name": "pix-create", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"account_key\": \"6d30a0b1-cb90-4ceb-b1ea-5bd600cdf3c8\",\n    \"pix_key_type\": \"cnpj\",\n    \"pix_key\": \"**************\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/app/bank-account/pix/keys", "host": ["{{url}}"], "path": ["app", "bank-account", "pix", "keys"]}, "description": "Esse é o endpoint inicial, chamado ao acessar o cury bank.\n\nO JSON retornado muda de acordo com o status da conta:\n\n- Status `initial`\n    \n\nÉ o endpoint inicial, quando o usuário não realizou nenhuma ação. O usuário deve ser direcionado apra a primeira tela (a do aceite de termos)\n\n``` json\n{\n    \"status\": \"initial\",\n    \"terms_url\": \"http://corretor-cury.localhost:8088/storage/test.pdf\"\n}\n\n ```\n\n- Status `terms_accepted`\n    \n\nO usuário chega nesse status após aceitar os termos de uso. Ele deve ser direcionado para a primeira tela do cadastro de documentos. Nesse endpoint a gente retorna os dados que temos pré-preenchidos do formulário:\n\n``` json\n{\n    \"status\": \"terms_accepted\",\n    \"user\": {\n        \"name\": \"Dr. <PERSON>uza Furtado\",\n        \"document_number\": \"670.020.580-88\",\n        \"email\": \"<EMAIL>\",\n        \"cellphone\": \"(74) 99271-5002\",\n        \"birthdate\": \"1985-11-28\",\n        \"mother_name\": null,\n        \"is_pep\": null,\n        \"nationality\": null,\n        \"monthly_income\": null,\n        \"address\": {\n            \"street\": \"Rua <PERSON>o Sanches\",\n            \"number\": \"29\",\n            \"complement\": null,\n            \"neighborhood\": \"Juliane d'Oeste\",\n            \"city\": \"Kevin do Norte\",\n            \"state\": \"PE\",\n            \"postal_code\": \"69541-774\"\n        }\n    }\n}\n\n ```\n\n- Status `creating_account`, `creating_account_reproved`, `creating_account_analysis`, `account_created`, `waiting_contract`, `waiting_salesforce_contact_id`\n    \n\nTodos esses são status após a gente iniciar a abertura de conta. Provavelmente a gente vai ter telas diferentes para alguns desses casos depois, mas por hora todos esses status vamos direcionar para aquela tela de \"aguarde\", após preencher o formulário.\n\n``` json\n{\n    \"status\": \"creating_account ou outro status\",\n    \"status_info\": {\n        \"type\": \"info\" ou \"error\",\n        \"message\": \"Mensagem aqui...\"\n    }\n}\n\n ```\n\n**Importante:** Quando existir esse \"status_info\", precisa exibir aquele box de status na tela de \"aguarde\" (ver no Figma).\n\nO campo `status_info.type` informa se o box é de informação ou de erro, e o `status_info.type` é o texto que deve ser exbidio.\n\n- Status `account_confirmed`\n    \n\nEsse é o status de quando a conta está criada e pronta para uso. Quando tiver esses status devemos direrecionar o user para a home do Cury Bank. Futuramente esse endpoint irá retornar outras informações.\n\n``` json\n{\n    \"status\": \"account_confirmed\",\n}\n\n ```"}, "response": []}], "description": "Endpoints de Cury Bank"}, {"name": "(Auth) <PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["const responseJson = pm.response.json();", "pm.collectionVariables.set(\"token\", responseJson.access_token);", ""], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disabledSystemHeaders": {"accept": true}}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "cpf", "value": "989.061.090-67", "type": "text", "disabled": true}, {"key": "password", "value": "TestX?97", "type": "text", "disabled": true}, {"key": "cpf", "value": "862.053.916-78", "type": "text", "disabled": true}, {"key": "password", "value": "admin1234", "type": "text", "disabled": true}, {"key": "cpf", "value": "159.426.861-49", "type": "text", "disabled": true}, {"key": "password", "value": "TestX?97", "type": "text", "disabled": true}, {"key": "cpf", "value": "108.330.015-66", "description": "Corretor <PERSON> (<PERSON><PERSON>)", "type": "text", "disabled": true}, {"key": "cpf", "value": "157.684.690-36", "description": "Corretor <PERSON> (<PERSON>)", "type": "text", "disabled": true}, {"key": "cpf", "value": "676.288.354-06", "description": "Corretor Homolog (exclusão)", "type": "text", "disabled": true}, {"key": "cpf", "value": "741.188.611-43", "description": "Corretor produção (senha padrão)", "type": "text", "disabled": true}, {"key": "cpf", "value": "547.629.330-23", "description": "Corretor Lucas homolog", "type": "text", "disabled": true}, {"key": "password", "value": "QPGndgut48", "description": "Corretor Lucas homolog", "type": "text", "disabled": true}, {"key": "cpf", "value": "547.629.330-23", "description": "Corretor will homolog", "type": "text", "disabled": true}, {"key": "password", "value": "QPGndgut48", "description": "Corretor will homolog", "type": "text", "disabled": true}, {"key": "cpf", "value": "024.640.600-38", "description": "Corretor local", "type": "text", "disabled": true}, {"key": "password", "value": "12345678", "description": "Corretor local", "type": "text", "disabled": true}, {"key": "cpf", "value": "351.623.731-76", "type": "text", "disabled": true}, {"key": "password", "value": "TestX?97", "type": "text", "disabled": true}, {"key": "cpf", "value": "784.642.587-04", "description": "Teste Maria Produção", "type": "text", "disabled": true}, {"key": "password", "value": "digitalusers987654@", "type": "text", "disabled": true}, {"key": "cpf", "value": "345.478.978-83", "type": "text", "disabled": true}, {"key": "password", "value": "<PERSON><PERSON><PERSON>", "type": "text", "disabled": true}, {"key": "cpf", "value": "616.574.446-00", "description": "Corre<PERSON> (<PERSON><PERSON>)", "type": "text", "disabled": true}, {"key": "password", "value": "maria123", "description": "Corretor <PERSON>", "type": "text", "disabled": true}, {"key": "cpf", "value": "115.792.457-30", "description": "<PERSON><PERSON> - PDV", "type": "text", "disabled": true}, {"key": "password", "value": "12345678", "type": "text", "disabled": true}, {"key": "password", "value": "123456", "type": "text", "disabled": true}, {"key": "cpf", "value": "345.478.978-83", "description": "CPF Will", "type": "text", "disabled": true}, {"key": "cpf", "value": "376.306.298-00", "description": "CPF homolog Repasses Salesforce", "type": "text", "disabled": true}, {"key": "cpf", "value": "234.327.030-90", "description": "CPF local homolog", "type": "text", "disabled": true}, {"key": "password", "value": "_teste_master", "type": "text", "disabled": true}, {"key": "cpf", "value": "999.999.999-99", "type": "text"}, {"key": "password", "value": "123123123", "type": "text"}, {"key": "password", "value": "bla123456", "type": "text", "disabled": true}]}, "url": {"raw": "{{url}}/app/auth/login", "host": ["{{url}}"], "path": ["app", "auth", "login"]}}, "response": []}, {"name": "(Auth) Logout", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL2NvcnJldG9yLWN1cnkubG9jYWw6NDQ0My9hcHAvYXV0aC9sb2dpbiIsImlhdCI6MTYxNTE3MTg0OSwiZXhwIjoxNjE2NDY3ODQ5LCJuYmYiOjE2MTUxNzE4NDksImp0aSI6InZ4SHdUZkx2UEcwN1NZSmMiLCJzdWIiOiI1ZmM4MGNhYjRmMzE4NzYyM2ExMjIyZGIiLCJwcnYiOiIzNWFmOTMwNzljNTQ1NjZjZTNhNjFiMmU4YTA0NjI1MWU2ZDE3NzBjIn0.he2FZmZ3atkLbQcGTrGdGvZ1ZGgYrnVW9g9kNP91yG4", "type": "string"}]}, "method": "POST", "header": [{"key": "Authorization", "value": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvY29ycmV0b3ItY3VyeS5sb2NhbDo0NDQzXC9hcHBcL2F1dGhcL2xvZ2luIiwiaWF0IjoxNjA1NDA0ODU5LCJleHAiOjE2MDU0MDg0NTksIm5iZiI6MTYwNTQwNDg1OSwianRpIjoidTJubHF6d3BMN05pemtmMyIsInN1YiI6IjVmYjA3OWUyMDM5NjNmMGY4NTY4Nzc2NSIsInBydiI6IjM1YWY5MzA3OWM1NDU2NmNlM2E2MWIyZThhMDQ2MjUxZTZkMTc3MGMifQ.QL2CvM3FLGIbis2xILpJCEbskM2KnGnjuaNHjH2knMg", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "email", "value": "<EMAIL>", "type": "text", "disabled": true}, {"key": "password", "value": "TestX?97", "type": "text"}, {"key": "email", "value": "<EMAIL>", "type": "text"}]}, "url": {"raw": "{{url}}/app/auth/logout", "host": ["{{url}}"], "path": ["app", "auth", "logout"]}}, "response": []}, {"name": "(Auth) Refresh <PERSON>", "event": [{"listen": "test", "script": {"exec": ["const responseJson = pm.response.json();", "", "if(responseJson.access_token){", "    pm.collectionVariables.set(\"token\", responseJson.access_token);", "}", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "", "type": "text", "disabled": true}, {"key": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text", "disabled": true}], "body": {"mode": "formdata", "formdata": [{"key": "email", "value": "<EMAIL>", "type": "text", "disabled": true}, {"key": "password", "value": "TestX?97", "type": "text", "disabled": true}, {"key": "email", "value": "<EMAIL>", "type": "text", "disabled": true}]}, "url": {"raw": "{{url}}/app/auth/refresh", "host": ["{{url}}"], "path": ["app", "auth", "refresh"]}}, "response": []}, {"name": "(Auth) <PERSON><PERSON>", "protocolProfileBehavior": {"disabledSystemHeaders": {"accept": true}}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "cpf", "value": "35746996890", "type": "text"}]}, "url": {"raw": "{{url}}/app/auth/reset-senha", "host": ["{{url}}"], "path": ["app", "auth", "reset-senha"]}}, "response": []}, {"name": "Cadastro (lista de regionais)", "request": {"method": "GET", "header": [], "url": {"raw": "{{url}}/app/cadastro/regionais?gerentes=0", "host": ["{{url}}"], "path": ["app", "cadastro", "regionais"], "query": [{"key": "gere<PERSON>", "value": "0"}]}, "description": "Retorna regionais e gerentes cadastrados"}, "response": []}, {"name": "Cadastro (canais)", "request": {"method": "GET", "header": [], "url": {"raw": "{{url}}/app/cadastro/canais", "host": ["{{url}}"], "path": ["app", "cadastro", "canais"]}, "description": "Retorna regionais e gerentes cadastrados"}, "response": []}, {"name": "Cadastro (imobiliárias)", "request": {"method": "GET", "header": [], "url": {"raw": "{{url}}/app/cadastro/imobiliarias?regional_id=2f8cc843-82ba-4599-a2fd-882bf8398488", "host": ["{{url}}"], "path": ["app", "cadastro", "imobiliarias"], "query": [{"key": "regional_id", "value": "2f8cc843-82ba-4599-a2fd-882bf8398488"}]}, "description": "Retorna regionais e gerentes cadastrados"}, "response": []}, {"name": "Cadastro (gerentes)", "request": {"method": "GET", "header": [], "url": {"raw": "{{url}}/app/cadastro/gerentes?regional_id=2f8cc843-82ba-4599-a2fd-882bf8398488&canal=CIA/Imobiliária&imobiliaria_id=51d43600-2167-404b-8945-c714d93b28b0", "host": ["{{url}}"], "path": ["app", "cadastro", "gere<PERSON>"], "query": [{"key": "regional_id", "value": "2f8cc843-82ba-4599-a2fd-882bf8398488"}, {"key": "canal", "value": "CIA/Imobiliária"}, {"key": "imobiliaria_id", "value": "51d43600-2167-404b-8945-c714d93b28b0"}]}, "description": "Retorna regionais e gerentes cadastrados"}, "response": []}, {"name": "Cadastro (verificar CPF)", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "cpf", "value": "207.641.081-31", "type": "text"}]}, "url": {"raw": "{{url}}/app/cadastro/check-cpf", "host": ["{{url}}"], "path": ["app", "cadastro", "check-cpf"]}, "description": "Retorna regionais e gerentes cadastrados"}, "response": []}, {"name": "Cadastro (verificar e-mail)", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "email", "value": "<EMAIL>", "type": "text", "disabled": true}, {"key": "email", "value": "<EMAIL>", "type": "text"}]}, "url": {"raw": "{{url}}/app/cadastro/check-email", "host": ["{{url}}"], "path": ["app", "cadastro", "check-email"]}, "description": "Verifica se o e-mail informado está disponível"}, "response": []}, {"name": "Cadastro (verificar apelido)", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "apelido", "value": "Carlafg", "type": "text"}]}, "url": {"raw": "{{url}}/app/cadastro/check-apelido", "host": ["{{url}}"], "path": ["app", "cadastro", "check-apelido"]}, "description": "Verifica se o apelido informado está disponível"}, "response": []}, {"name": "Cadastro (sugestão de apelidos)", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "{{url}}/app/cadastro/sugestao-apelidos", "host": ["{{url}}"], "path": ["app", "cadastro", "sugestao-apelidos"]}, "description": "Verifica se o apelido informado está disponível\n\n```json\n[\n    \"Apelido 1\",\n    \"Apelido 2\",\n    \"Apelido 3\"\n]\n```"}, "response": []}, {"name": "Cadastro (endereço)", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "cep", "value": "000", "description": "erro", "type": "text", "disabled": true}, {"key": "cep", "value": "01311-936", "description": "sucesso", "type": "text", "disabled": true}, {"key": "cep", "value": "01311-100", "description": "sucesso (com complemento)", "type": "text"}]}, "url": {"raw": "{{url}}/app/cadastro/endereco", "host": ["{{url}}"], "path": ["app", "cadastro", "endereco"]}, "description": "Retorna endereço à partir do CEP"}, "response": []}, {"name": "Cadastro (upload)", "request": {"method": "POST", "header": [{"key": "X-EMAIL-CMS", "value": "<EMAIL>", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "documento", "type": "file", "src": "/home/<USER>/Imagens/Nature Photo Pack/Vivid <PERSON>.jpg", "disabled": true}, {"key": "creci", "type": "file", "src": "/home/<USER>/Imagens/_marcação/1500x1000_03.png"}, {"key": "creci", "type": "file", "src": "/home/<USER>/Imagens/Nature Photo Pack/Sapa Sunrise.jpg", "disabled": true}, {"key": "documento", "type": "file", "src": "/home/<USER>/Imagens/_marcação/600x562.jpg", "disabled": true}, {"key": "selfie", "value": "", "type": "text", "disabled": true}]}, "url": {"raw": "{{url}}/app/cadastro/upload", "host": ["{{url}}"], "path": ["app", "cadastro", "upload"]}}, "response": []}, {"name": "Cadastro (tamanhos de camisa)", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "X-EMAIL-CMS", "value": "<EMAIL>", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "documento", "type": "file", "src": "/home/<USER>/Imagens/Nature Photo Pack/Vivid <PERSON>.jpg", "disabled": true}, {"key": "creci", "type": "file", "src": "/home/<USER>/Imagens/_marcação/1500x1000_03.png"}, {"key": "creci", "type": "file", "src": "/home/<USER>/Imagens/Nature Photo Pack/Sapa Sunrise.jpg", "disabled": true}, {"key": "documento", "type": "file", "src": "/home/<USER>/Imagens/_marcação/600x562.jpg", "disabled": true}, {"key": "selfie", "value": "", "type": "text", "disabled": true}]}, "url": {"raw": "{{url}}/app/cadastro/camisa-tamanhos", "host": ["{{url}}"], "path": ["app", "cadastro", "camisa-tamanhos"]}, "description": "```json\n{\n    \"camisa_tamanhos\": [\n        \"P\",\n        \"M\",\n        \"G\",\n        \"2G\",\n        \"3G\"\n    ]\n}\n```"}, "response": []}, {"name": "Cadastro", "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "corretor_cury", "value": "1", "description": "0 ou 1", "type": "text"}, {"key": "cpf", "value": "249.150.940-73", "type": "text"}, {"key": "nome", "value": "Nome do corretor", "type": "text"}, {"key": "apelido", "value": "Gustavo123d1", "type": "text"}, {"key": "data_nascimento", "value": "1988-07-28", "type": "text"}, {"key": "regional_id", "value": "01b52eea-d4ed-4829-83d6-3804ae80f318", "description": "obrigatório se corretor_cury = 1", "type": "text"}, {"key": "gerente_id", "value": "9f13a4fc-08e5-43e0-9ac8-2588488a159d", "description": "obrigatório se corretor_cury = 1", "type": "text"}, {"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "telefone", "value": "(11) 1111-1111", "type": "text"}, {"key": "cep", "value": "08487-887", "type": "text", "disabled": true}, {"key": "endereco", "value": "<PERSON><PERSON>ua", "type": "text", "disabled": true}, {"key": "numero", "value": "123", "type": "text", "disabled": true}, {"key": "complemento", "value": "", "description": "opcional", "type": "text", "disabled": true}, {"key": "bairro", "value": "Bairro", "type": "text", "disabled": true}, {"key": "cidade", "value": "Cidade", "type": "text", "disabled": true}, {"key": "uf", "value": "RJ", "type": "text", "disabled": true}, {"key": "documento_numero", "value": "111.454.78-9", "type": "text"}, {"key": "creci_numero", "value": "7414751.45", "type": "text"}, {"key": "documento_id", "value": "812672c4-287e-49f6-aa53-b7cda89cb6e1", "description": "ID retornado pelo endpoint do upload", "type": "text"}, {"key": "creci_id", "value": "ce4951cb-92fb-45d6-b6e8-f7956eb61d31", "description": "ID retornado pelo endpoint do upload", "type": "text"}, {"key": "endereco[logradouro]", "value": "asdasdsad", "type": "text", "disabled": true}, {"key": "password", "value": "", "type": "text"}, {"key": "password_confirm", "value": "", "type": "text"}, {"key": "selfie_id", "value": "", "type": "text"}, {"key": "camisa_tamanho", "value": "G", "type": "text"}]}, "url": {"raw": "{{url}}/app/cadastro", "host": ["{{url}}"], "path": ["app", "cadastro"]}}, "response": []}, {"name": "Cadastro (verificar status do cadastro)", "request": {"method": "GET", "header": [], "url": {"raw": "{{url}}/app/cadastro/check/4d5e8b03-79a8-40f7-845b-f6c0b02da7f8", "host": ["{{url}}"], "path": ["app", "cadastro", "check", "4d5e8b03-79a8-40f7-845b-f6c0b02da7f8"]}, "description": "Verifica status do cadastro informado"}, "response": []}, {"name": "(App) User", "protocolProfileBehavior": {"disabledSystemHeaders": {"accept": true}}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvY29ycmV0b3ItY3VyeS5sb2NhbDo0NDQzXC9hcHBcL2F1dGhcL2xvZ2luIiwiaWF0IjoxNjA2MTExMjI5LCJleHAiOjE2MDYxMTQ4MjksIm5iZiI6MTYwNjExMTIyOSwianRpIjoiTzBlbUtXdlhFbnoyc3pXZyIsInN1YiI6IjVmYjllMDVhNTQwYTllMGQwMjBmMGM5NSIsInBydiI6IjM1YWY5MzA3OWM1NDU2NmNlM2E2MWIyZThhMDQ2MjUxZTZkMTc3MGMifQ.r5Y37RqrrzpL3XNmFPdruQe4SzBPmPxYxl4gC4crvTo", "type": "text", "disabled": true}, {"key": "appcury-base-os", "value": "Postman", "type": "text"}, {"key": "appcury-system-version", "value": "12", "type": "text"}, {"key": "appcury-readable-version", "value": "5.5.5", "type": "text"}], "url": {"raw": "https://econ.imobiwox.com.br/api/v0/app/user", "protocol": "https", "host": ["econ", "imobiwox", "com", "br"], "path": ["api", "v0", "app", "user"]}}, "response": []}, {"name": "(App) Excluir User", "protocolProfileBehavior": {"disabledSystemHeaders": {"accept": true}}, "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvY29ycmV0b3ItY3VyeS5sb2NhbDo0NDQzXC9hcHBcL2F1dGhcL2xvZ2luIiwiaWF0IjoxNjA2MTExMjI5LCJleHAiOjE2MDYxMTQ4MjksIm5iZiI6MTYwNjExMTIyOSwianRpIjoiTzBlbUtXdlhFbnoyc3pXZyIsInN1YiI6IjVmYjllMDVhNTQwYTllMGQwMjBmMGM5NSIsInBydiI6IjM1YWY5MzA3OWM1NDU2NmNlM2E2MWIyZThhMDQ2MjUxZTZkMTc3MGMifQ.r5Y37RqrrzpL3XNmFPdruQe4SzBPmPxYxl4gC4crvTo", "type": "text"}], "url": {"raw": "{{url}}/app/user", "host": ["{{url}}"], "path": ["app", "user"]}}, "response": []}, {"name": "(App) Listagem de plantões", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "", "type": "text", "disabled": true}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "{{url}}/app/plantao?latitude=-23.550641&longitude=-46.610286", "host": ["{{url}}"], "path": ["app", "plantao"], "query": [{"key": "latitude", "value": "-23.56429922395802", "disabled": true}, {"key": "longitude", "value": "-46.60854434203625", "disabled": true}, {"key": "latitude", "value": "-22.759602122709552", "disabled": true}, {"key": "longitude", "value": "-43.421638650134106", "disabled": true}, {"key": "latitude", "value": "-23.550641"}, {"key": "longitude", "value": "-46.610286"}]}}, "response": []}, {"name": "(App) Plantões - Contato", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "", "type": "text", "disabled": true}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "{{url}}/app/plantoes/fe45ab26-2928-4a5a-99f3-0699443b17ab/contato", "host": ["{{url}}"], "path": ["app", "plantoes", "fe45ab26-2928-4a5a-99f3-0699443b17ab", "contato"]}}, "response": []}, {"name": "(App) Checkin - verificar local", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL2hvbW9sb2ctYXBwLmN1cnkubmV0L2FwcC9hdXRoL2xvZ2luIiwiaWF0IjoxNjI0Mjg4MDkxLCJleHAiOjE2MjU1ODQwOTEsIm5iZiI6MTYyNDI4ODA5MSwianRpIjoiM25SZGxpNlpZOGpGTXF1USIsInN1YiI6IjYwZDBhYjQ2ZWI1YTY0NzY2YjJhMmU2MiIsInBydiI6IjM1YWY5MzA3OWM1NDU2NmNlM2E2MWIyZThhMDQ2MjUxZTZkMTc3MGMifQ.6kSnYHrWUlCaZQRH6NxZjIjnHTKACEaOy6qBu9odTpo", "type": "string"}]}, "method": "POST", "header": [{"key": "Authorization", "value": "", "type": "text", "disabled": true}], "body": {"mode": "formdata", "formdata": [{"key": "latitude", "value": "-23.64432006442319", "type": "text", "disabled": true}, {"key": "longitude", "value": "-46.73455702785719", "type": "text", "disabled": true}, {"key": "latitude", "value": "-23.56429922395802", "type": "text", "disabled": true}, {"key": "longitude", "value": "-46.60854434203625", "type": "text", "disabled": true}, {"key": "latitude", "value": "-23.594261086503", "type": "text"}, {"key": "longitude", "value": "-46.689549231834", "type": "text"}]}, "url": {"raw": "{{url}}/app/checkin/verificar", "host": ["{{url}}"], "path": ["app", "checkin", "verificar"]}}, "response": []}, {"name": "(App) Checkin", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "", "type": "text", "disabled": true}], "body": {"mode": "formdata", "formdata": [{"key": "latitude", "value": "-23.559311", "type": "text"}, {"key": "longitude", "value": "-46.613687", "type": "text"}, {"key": "plantao_id", "value": "5cd88832-9c54-42fe-9f60-d341589dff72", "type": "text"}, {"key": "plantao_id", "value": "a6ede2a5-f070-4d24-a310-4ea423ff2be6", "type": "text", "disabled": true}]}, "url": {"raw": "{{url}}/app/checkin", "host": ["{{url}}"], "path": ["app", "checkin"]}}, "response": []}, {"name": "(App) Checkout", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL2NvcnJldG9yLWN1cnkubG9jYWw6NDQ0My9hcHAvYXV0aC9sb2dpbiIsImlhdCI6MTYxNTM0MTEzOCwiZXhwIjoxNjE2NjM3MTM4LCJuYmYiOjE2MTUzNDExMzgsImp0aSI6ImdCcXBCcFBZVmVHbzkxeTEiLCJzdWIiOiI1ZmM4MGUyNzRmMzE4NzYyM2ExMjI0OWUiLCJwcnYiOiIzNWFmOTMwNzljNTQ1NjZjZTNhNjFiMmU4YTA0NjI1MWU2ZDE3NzBjIn0.58Ta5ZyyFVb5dZnWwKi_sBZPz3X5frnk71unHcDjOE0", "type": "string"}]}, "method": "POST", "header": [{"key": "Authorization", "value": "", "type": "text", "disabled": true}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "{{url}}/app/checkout", "host": ["{{url}}"], "path": ["app", "checkout"]}, "description": "Remove corretor da fila e realiza checkout."}, "response": []}, {"name": "(App) Fila", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL2hvbW9sb2ctYXBwLmN1cnkubmV0L2FwcC9hdXRoL2xvZ2luIiwiaWF0IjoxNjEyODk0OTIxLCJleHAiOjE2MTQxOTA5MjEsIm5iZiI6MTYxMjg5NDkyMSwianRpIjoiSnZLc1ZkQllyOURuWFU0USIsInN1YiI6IjYwMjI5NGM2OTBkNGRkNTk5MTc2NGE2YSIsInBydiI6IjM1YWY5MzA3OWM1NDU2NmNlM2E2MWIyZThhMDQ2MjUxZTZkMTc3MGMifQ.lqGraXW2F_aHKDrRaQ8ZKz0WQ198VojMGberEGGDSAM", "type": "string"}]}, "method": "GET", "header": [{"key": "Authorization", "value": "", "type": "text", "disabled": true}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "{{url}}/app/fila", "host": ["{{url}}"], "path": ["app", "fila"]}, "description": "Informações do corretor na fila."}, "response": []}, {"name": "(App) Fila - listagem", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL2hvbW9sb2ctYXBwLmN1cnkubmV0L2FwcC9hdXRoL2xvZ2luIiwiaWF0IjoxNjE0MzY4OTQzLCJleHAiOjE2MTU2NjQ5NDMsIm5iZiI6MTYxNDM2ODk0MywianRpIjoiRENpd2NvR28ycDBubVpVYiIsInN1YiI6IjYwMjI5NGM2OTBkNGRkNTk5MTc2NGE2YyIsInBydiI6IjM1YWY5MzA3OWM1NDU2NmNlM2E2MWIyZThhMDQ2MjUxZTZkMTc3MGMifQ.14SQkz7tc71BQkVahuoZa-uM58A5a7u20xEmYQw_UiI", "type": "string"}]}, "method": "GET", "header": [{"key": "Authorization", "value": "", "type": "text", "disabled": true}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "{{url}}/app/fila/lista", "host": ["{{url}}"], "path": ["app", "fila", "lista"]}, "description": "Lista de corretores na fila."}, "response": []}, {"name": "(App) Atendimento - Iniciar", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL2NvcnJldG9yLWN1cnkubG9jYWw6NDQ0My9hcHAvYXV0aC9sb2dpbiIsImlhdCI6MTYyMTU3MDAxNSwiZXhwIjoxNjIyODY2MDE1LCJuYmYiOjE2MjE1NzAwMTUsImp0aSI6IngzQjB0NTNpNlp1NDNmVWoiLCJzdWIiOiI1ZmM4MGU0NDRmMzE4NzYyM2ExMjI1NTIiLCJwcnYiOiIzNWFmOTMwNzljNTQ1NjZjZTNhNjFiMmU4YTA0NjI1MWU2ZDE3NzBjIn0.C0kOqZq1AJKxPaXgqzhmamyGrR4EE2x-wJgDU2kc9oc", "type": "string"}]}, "method": "POST", "header": [{"key": "Authorization", "value": "", "type": "text", "disabled": true}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "{{url}}/app/atendimento/iniciar", "host": ["{{url}}"], "path": ["app", "atendimento", "iniciar"]}, "description": "Endpoint para iniciar um atendimento"}, "response": []}, {"name": "(App) Atendimento - Encerrar", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL2hvbW9sb2ctYXBwLmN1cnkubmV0L2FwcC9hdXRoL2xvZ2luIiwiaWF0IjoxNjE0ODg2MTQyLCJleHAiOjE2MTYxODIxNDIsIm5iZiI6MTYxNDg4NjE0MiwianRpIjoiQnhjV054a05WQ1hsZ1RodSIsInN1YiI6IjYwNDEzNDNjZGJiY2ZkMWU5ZDQ4NWMxZiIsInBydiI6IjM1YWY5MzA3OWM1NDU2NmNlM2E2MWIyZThhMDQ2MjUxZTZkMTc3MGMifQ.F5zAMiK0yFhgn71kWLR5RBAyTOWg7FHe-h4K9QGcQrI", "type": "string"}]}, "method": "POST", "header": [{"key": "Authorization", "value": "", "type": "text", "disabled": true}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "{{url}}/app/atendimento/encerrar", "host": ["{{url}}"], "path": ["app", "atendimento", "encerrar"]}, "description": "Endpoint para encerrar um atendimento"}, "response": []}, {"name": "(App) Notificações", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "", "type": "text", "disabled": true}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "{{url}}/app/notificacoes", "host": ["{{url}}"], "path": ["app", "notificacoes"]}, "description": "Listagem de Notificações"}, "response": []}, {"name": "(App) Notificações - Interna", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "", "type": "text", "disabled": true}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "{{url}}/app/notificacoes/e97ce9ba-9369-46bc-a858-99a23504ce9b", "host": ["{{url}}"], "path": ["app", "notificacoes", "e97ce9ba-9369-46bc-a858-99a23504ce9b"]}, "description": "Interna de Notificações"}, "response": []}, {"name": "(App) Notificações - Excluir", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL2hvbW9sb2ctYXBwLmN1cnkubmV0IiwiaWF0IjoxNjE5MjEwMjU3LCJleHAiOjE2MjA1MDYyNTcsIm5iZiI6MTYxOTIxMDI1NywianRpIjoic01BWTIwdXZ2eUg4N3h2MyIsInN1YiI6IjVmYzhlM2UzZDgyZDA0Mjg4YzIzNWUzYSIsInBydiI6IjM1YWY5MzA3OWM1NDU2NmNlM2E2MWIyZThhMDQ2MjUxZTZkMTc3MGMifQ.Zu8Fmo93SdwXb0BhvNLvJ0lcjjatsZS6oPMFRnz7T6M", "type": "string"}]}, "method": "DELETE", "header": [{"key": "Authorization", "value": "", "type": "text", "disabled": true}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "{{url}}/app/notificacoes/878e2098-d157-432f-9bc2-008f8dbaa7e2", "host": ["{{url}}"], "path": ["app", "notificacoes", "878e2098-d157-432f-9bc2-008f8dbaa7e2"]}, "description": "Excluir Notificações"}, "response": []}, {"name": "(Plantão) Login", "protocolProfileBehavior": {"disabledSystemHeaders": {"accept": true}}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "id", "value": "a6ede2a5-f070-4d24-a310-4ea423ff2be6", "type": "text"}, {"key": "password", "value": "8fOCpaV8Xt8q", "type": "text"}]}, "url": {"raw": "{{url}}/plantao/auth/login", "host": ["{{url}}"], "path": ["plantao", "auth", "login"]}}, "response": []}, {"name": "(App) Treinamentos", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL2hvbW9sb2ctYXBwLmN1cnkubmV0L2FwcC9hdXRoL2xvZ2luIiwiaWF0IjoxNjI0Mjg4MDkxLCJleHAiOjE2MjU1ODQwOTEsIm5iZiI6MTYyNDI4ODA5MSwianRpIjoiM25SZGxpNlpZOGpGTXF1USIsInN1YiI6IjYwZDBhYjQ2ZWI1YTY0NzY2YjJhMmU2MiIsInBydiI6IjM1YWY5MzA3OWM1NDU2NmNlM2E2MWIyZThhMDQ2MjUxZTZkMTc3MGMifQ.6kSnYHrWUlCaZQRH6NxZjIjnHTKACEaOy6qBu9odTpo", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{url}}/app/treinamentos", "host": ["{{url}}"], "path": ["app", "treinamentos"]}, "description": "* 200\n```json\n{\n    \"treinamentos\": [\n        {\n            \"id\": \"b52644ea-543b-4dbc-8cf2-e8cae0c8f19a\",\n            \"titulo\": \"Est quo nulla vel. (PDF)\",\n            \"status_andamento\": \"Pendente\",\n            \"created_at\": \"2021-06-21 11:38:44\"\n        },\n        {\n            \"id\": \"c4865658-7096-4874-9bea-77c1242c3122\",\n            \"titulo\": \"Non pariatur aut doloremque. (PDF)\",\n            \"status_andamento\": \"Pendente\",\n            \"created_at\": \"2021-06-21 11:38:44\"\n        }\n    ]\n}\n```\n"}, "response": []}, {"name": "(App) Treinamentos - Home", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL2hvbW9sb2ctYXBwLmN1cnkubmV0L2FwcC9hdXRoL2xvZ2luIiwiaWF0IjoxNjI0Mjg4MDkxLCJleHAiOjE2MjU1ODQwOTEsIm5iZiI6MTYyNDI4ODA5MSwianRpIjoiM25SZGxpNlpZOGpGTXF1USIsInN1YiI6IjYwZDBhYjQ2ZWI1YTY0NzY2YjJhMmU2MiIsInBydiI6IjM1YWY5MzA3OWM1NDU2NmNlM2E2MWIyZThhMDQ2MjUxZTZkMTc3MGMifQ.6kSnYHrWUlCaZQRH6NxZjIjnHTKACEaOy6qBu9odTpo", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{url}}/app/treinamentos/58b61442-aa1d-4ae0-9fb8-0ef9f01d6d95", "host": ["{{url}}"], "path": ["app", "treinamentos", "58b61442-aa1d-4ae0-9fb8-0ef9f01d6d95"]}, "description": "* 200\n```json\n{\n    \"treinamento\": {\n        \"id\": \"b52644ea-543b-4dbc-8cf2-e8cae0c8f19a\",\n        \"titulo\": \"Est quo nulla vel. (PDF)\",\n        \"status_andamento\": \"Pendente\",\n        \"created_at\": \"2021-06-21 11:38:44\",\n        \"descricao\": \"Esse totam quia molestiae unde ratione. Aspernatur unde odio est nesciunt. Aperiam velit quis neque in.\",\n        \"conteudo\": {\n            \"tipo\": \"pdf\",\n            \"titulo\": \"Nome original do PDF.pdf\",\n            \"pdf_path\": \"treinamentos/exemplo.pdf\",\n            \"pdf_url\": \"https://corretor-cury.local:4443/storage/treinamentos/exemplo.pdf\"\n        },\n        \"has_avaliacao\": false\n    }\n}\n```\n\n- Se o user concluíu o treinamento, status_andamento = \"Concluído\"\n\n- Se o treinamento tiver avaliação, has_avaliacao = true\n\n- Se o treinamento for em vídeo (youtube), o conteudo será:\n```json\n\"conteudo\": {\n            \"tipo\": \"youtube\",\n            \"titulo\": null,\n            \"youtube_url\": \"https://www.youtube.com/watch?v=jB-2l1wrHHA\",\n            \"youtube_id\": \"jB-2l1wrHHA\"\n        },\n```"}, "response": []}, {"name": "(App) Treinamentos - Conteúdo", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL2hvbW9sb2ctYXBwLmN1cnkubmV0L2FwcC9hdXRoL2xvZ2luIiwiaWF0IjoxNjI0Mjg4MDkxLCJleHAiOjE2MjU1ODQwOTEsIm5iZiI6MTYyNDI4ODA5MSwianRpIjoiM25SZGxpNlpZOGpGTXF1USIsInN1YiI6IjYwZDBhYjQ2ZWI1YTY0NzY2YjJhMmU2MiIsInBydiI6IjM1YWY5MzA3OWM1NDU2NmNlM2E2MWIyZThhMDQ2MjUxZTZkMTc3MGMifQ.6kSnYHrWUlCaZQRH6NxZjIjnHTKACEaOy6qBu9odTpo", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{url}}/app/treinamentos/58b61442-aa1d-4ae0-9fb8-0ef9f01d6d95/conteudo", "host": ["{{url}}"], "path": ["app", "treinamentos", "58b61442-aa1d-4ae0-9fb8-0ef9f01d6d95", "conteudo"]}, "description": "- Endpoint chamado após o user clicar em \"Iniciar Treinamento\". Re<PERSON><PERSON> o mesmo json da home do treinamento, a diferença é que no backend eu uso esse endpoint para marcar o treinamento como \"concluído\" caso ele não tenha avaliação.\n\n* 200\n```json\n{\n    \"treinamento\": {\n        \"id\": \"b52644ea-543b-4dbc-8cf2-e8cae0c8f19a\",\n        \"titulo\": \"Est quo nulla vel. (PDF)\",\n        \"status_andamento\": \"Pendente\",\n        \"created_at\": \"2021-06-21 11:38:44\",\n        \"descricao\": \"Esse totam quia molestiae unde ratione. Aspernatur unde odio est nesciunt. Aperiam velit quis neque in.\",\n        \"conteudo\": {\n            \"tipo\": \"pdf\",\n            \"titulo\": \"Nome original do PDF.pdf\",\n            \"pdf_path\": \"treinamentos/exemplo.pdf\",\n            \"pdf_url\": \"https://corretor-cury.local:4443/storage/treinamentos/exemplo.pdf\"\n        },\n        \"has_avaliacao\": false\n    }\n}\n```\n\n- Se o user concluíu o treinamento, status_andamento = \"Concluído\"\n\n- Se o treinamento tiver avaliação, has_avaliacao = true\n\n- Se o treinamento for em vídeo (youtube), o conteudo será:\n```json\n\"conteudo\": {\n            \"tipo\": \"youtube\",\n            \"titulo\": null,\n            \"youtube_url\": \"https://www.youtube.com/watch?v=jB-2l1wrHHA\",\n            \"youtube_id\": \"jB-2l1wrHHA\"\n        },\n```"}, "response": []}, {"name": "(App) Treinamentos - Avaliação", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL2hvbW9sb2ctYXBwLmN1cnkubmV0L2FwcC9hdXRoL2xvZ2luIiwiaWF0IjoxNjI0Mjg4MDkxLCJleHAiOjE2MjU1ODQwOTEsIm5iZiI6MTYyNDI4ODA5MSwianRpIjoiM25SZGxpNlpZOGpGTXF1USIsInN1YiI6IjYwZDBhYjQ2ZWI1YTY0NzY2YjJhMmU2MiIsInBydiI6IjM1YWY5MzA3OWM1NDU2NmNlM2E2MWIyZThhMDQ2MjUxZTZkMTc3MGMifQ.6kSnYHrWUlCaZQRH6NxZjIjnHTKACEaOy6qBu9odTpo", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{url}}/app/treinamentos/028dcc43-f3a9-420d-9bb4-b13098a3192e/avaliacao", "host": ["{{url}}"], "path": ["app", "treinamentos", "028dcc43-f3a9-420d-9bb4-b13098a3192e", "avaliacao"]}, "description": "- Retorna questões da avaliação em ordem aleatória. Se o treinamento não tiver avaliação retorna 404. Se o user já tiver concluído o treinamento retorna 403.\n\n* 200\n```json\n{\n    \"treinamento\": {\n        \"id\": \"dc0ffc9c-9ecc-4710-aba6-548d4ff9942e\",\n        \"titulo\": \"In id laboriosam quia dicta. (PDF, com avaliao)\",\n        \"status_andamento\": \"Pendente\",\n        \"created_at\": \"2021-06-21 11:50:45\",\n        \"avaliacao\": [\n            {\n                \"id\": \"5c61d7aa-704c-4440-86ce-9aed9c43118c\",\n                \"titulo\": \"Et deserunt commodi aut quia.\",\n                \"alternativas\": [\n                    {\n                        \"titulo\": \"Maxime culpa nisi.\",\n                        \"id\": \"5c4ad926-c47f-381d-8c00-6fbc434cb542\"\n                    },\n                    {\n                        \"titulo\": \"Magnam dignissimos. (correta)\",\n                        \"id\": \"a8a1f790-8e09-3c68-98e6-e0f035ea074d\"\n                    },\n                    {\n                        \"titulo\": \"Itaque eum possimus.\",\n                        \"id\": \"55b37623-df3e-3635-a284-1cf2808c74c1\"\n                    },\n                    {\n                        \"titulo\": \"Qui maiores dolor.\",\n                        \"id\": \"2469744a-a59e-3b4e-bf06-f4ba96b7631b\"\n                    }\n                ]\n            },\n            {\n                \"id\": \"d09aeb7d-5936-458c-ac48-80bb3b59ff76\",\n                \"titulo\": \"Velit architecto accusantium minima iure.\",\n                \"alternativas\": [\n                    {\n                        \"titulo\": \"Dolor voluptatem.\",\n                        \"id\": \"f575782b-fae7-3226-8348-caeb31b952a0\"\n                    },\n                    {\n                        \"titulo\": \"Non et quis.\",\n                        \"id\": \"d565532a-d33c-3e37-87ac-63ed3910a739\"\n                    },\n                    {\n                        \"titulo\": \"Eos hic qui ratione.\",\n                        \"id\": \"bf00c951-c162-3da7-b966-0e31855c7c54\"\n                    },\n                    {\n                        \"titulo\": \"Voluptates dolorem. (correta)\",\n                        \"id\": \"32b845ec-2bc9-376e-a7b1-ca393cd314bf\"\n                    }\n                ]\n            },\n\n            ...\n\n        ]\n    }\n}\n```\n"}, "response": []}, {"name": "(App) Treinamentos - Enviar Avaliação", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL2hvbW9sb2ctYXBwLmN1cnkubmV0L2FwcC9hdXRoL2xvZ2luIiwiaWF0IjoxNjI0Mjg4MDkxLCJleHAiOjE2MjU1ODQwOTEsIm5iZiI6MTYyNDI4ODA5MSwianRpIjoiM25SZGxpNlpZOGpGTXF1USIsInN1YiI6IjYwZDBhYjQ2ZWI1YTY0NzY2YjJhMmU2MiIsInBydiI6IjM1YWY5MzA3OWM1NDU2NmNlM2E2MWIyZThhMDQ2MjUxZTZkMTc3MGMifQ.6kSnYHrWUlCaZQRH6NxZjIjnHTKACEaOy6qBu9odTpo", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "avaliacao[93764523-a7d3-423e-a927-d9042a9889b0]", "value": "aebd10a4-6145-35bb-ba56-bbe35133819c", "type": "text"}, {"key": "avaliacao[8b0e1e72-4021-4afc-be42-42a2675c2fc9]", "value": "2726d29a-cd7c-30a0-9255-58b31b11d446", "type": "text"}, {"key": "avaliacao[2f0c8ca6-5ecb-4ac8-8745-31139932cbab]", "value": "71c468d7-07f9-3c63-884e-e7e3f9463381", "type": "text"}, {"key": "avaliacao[59a7321f-fbbf-47d0-9836-639b7a6048f5]", "value": "incorreta", "type": "text"}, {"key": "avaliacao[289d1400-cc41-4f11-9329-8e1fcd6ca43c]", "value": "incorreta", "type": "text"}, {"key": "avaliacao[749dc39c-c6a0-4ef4-a4ca-363a57e73fcd]", "value": "incorreta", "type": "text"}, {"key": "avaliacao[d6459d21-3c08-4acb-bdf6-9ea492ddece0]", "value": "incorreta", "type": "text"}, {"key": "avaliacao[3a9b6eca-97de-472e-8313-ae10f48cedd0]", "value": "incorreta", "type": "text"}, {"key": "avaliacao[dd19a80f-5091-4c20-aaba-14e86f97882d]", "value": "incorreta", "type": "text"}, {"key": "avaliacao[b381dd64-09b8-4b71-a02e-b088cbf069ca]", "value": "incorreta", "type": "text"}]}, "url": {"raw": "{{url}}/app/treinamentos/028dcc43-f3a9-420d-9bb4-b13098a3192e/avaliacao", "host": ["{{url}}"], "path": ["app", "treinamentos", "028dcc43-f3a9-420d-9bb4-b13098a3192e", "avaliacao"]}, "description": "- A avaliação precisa ser enviada no formato:\n\navaliacao[ID_QUESTÃO] = ID_ALTERNATIVA\n\n- Se faltar alguma questão no POST, retorna 422.\n\n\n* 200 (user aprovado)\n```json\n{\n    \n    \"id\": \"dc0ffc9c-9ecc-4710-aba6-548d4ff9942e\",\n    \"titulo\": \"In id laboriosam quia dicta. (PDF, com avaliao)\",\n    \"status\": \"aprovado\",\n    \"porcentagem_acertos\": \"70%\"\n}\n```\n\n* 200 (user reprovado)\n```json\n{\n    \n    \"id\": \"dc0ffc9c-9ecc-4710-aba6-548d4ff9942e\",\n    \"titulo\": \"In id laboriosam quia dicta. (PDF, com avaliao)\",\n    \"status\": \"reprovado\",\n    \"porcentagem_acertos\": \"50%\"\n}\n```\n"}, "response": []}, {"name": "(App) CRM - Novo Lead - Ativar Cliente", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "avaliacao[93764523-a7d3-423e-a927-d9042a9889b0]", "value": "aebd10a4-6145-35bb-ba56-bbe35133819c", "type": "text"}, {"key": "avaliacao[8b0e1e72-4021-4afc-be42-42a2675c2fc9]", "value": "2726d29a-cd7c-30a0-9255-58b31b11d446", "type": "text"}, {"key": "avaliacao[2f0c8ca6-5ecb-4ac8-8745-31139932cbab]", "value": "71c468d7-07f9-3c63-884e-e7e3f9463381", "type": "text"}, {"key": "avaliacao[59a7321f-fbbf-47d0-9836-639b7a6048f5]", "value": "incorreta", "type": "text"}, {"key": "avaliacao[289d1400-cc41-4f11-9329-8e1fcd6ca43c]", "value": "incorreta", "type": "text"}, {"key": "avaliacao[749dc39c-c6a0-4ef4-a4ca-363a57e73fcd]", "value": "incorreta", "type": "text"}, {"key": "avaliacao[d6459d21-3c08-4acb-bdf6-9ea492ddece0]", "value": "incorreta", "type": "text"}, {"key": "avaliacao[3a9b6eca-97de-472e-8313-ae10f48cedd0]", "value": "incorreta", "type": "text"}, {"key": "avaliacao[dd19a80f-5091-4c20-aaba-14e86f97882d]", "value": "incorreta", "type": "text"}, {"key": "avaliacao[b381dd64-09b8-4b71-a02e-b088cbf069ca]", "value": "incorreta", "type": "text"}]}, "url": {"raw": "{{url}}/app/crm/ativar-cliente/13ade3c4-5260-4be9-81f9-1e957b9fc84f", "host": ["{{url}}"], "path": ["app", "crm", "ativar-cliente", "13ade3c4-5260-4be9-81f9-1e957b9fc84f"]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "url", "value": "http://corretor-cury.localhost:8088"}, {"key": "url", "value": "https://homolog-app.cury.net", "disabled": true}, {"key": "url", "value": "https://app.cury.net", "disabled": true}, {"key": "token", "value": "login"}, {"key": "token_teste", "value": "", "type": "string"}]}