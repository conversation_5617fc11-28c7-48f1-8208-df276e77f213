<?php

namespace Tests\Feature\BankAccount;

use App\Enums\BankAccount\PixTransferStatusEnum;
use App\Enums\BankAccount\PixTypeEnum;
use App\Enums\BankAccount\StatusEnum;
use App\Enums\BankAccount\TfaTypeEnum;
use App\Models\BankAccount;
use App\Models\PixTransfer;
use App\Models\UserApp;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

class PixTransferTest extends TestCase
{
    use RefreshDatabase;

    private UserApp $user;
    private BankAccount $bankAccount;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = UserApp::factory()->create([
            'password' => Hash::make('password123'),
        ]);

        $this->bankAccount = BankAccount::factory()
            ->user($this->user)
            ->create([
                'status' => StatusEnum::ACCOUNT_CONFIRMED,
                'is_active' => true,
            ]);
    }

    public function test_can_create_manual_pix_transfer()
    {
        $this->actingAs($this->user);

        $payload = [
            'ispb' => '********',
            'account_branch' => '1234',
            'account_number' => '********9',
            'account_digit' => '0',
            'account_type' => 'checking_account',
            'name' => 'João da Silva',
            'document_number' => '********901',
        ];

        $response = $this->postJson('/api/bank-account/transfers/pix/manual', $payload);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'request_control_key',
                'pix_type',
                'name',
                'document_number',
                'date',
                'account' => [
                    'ispb',
                    'bank_name',
                    'account_branch',
                    'account_number',
                    'account_digit',
                    'account_type',
                ],
                'favorite' => [
                    'is_favorite',
                    'name',
                ],
            ]);

        $this->assertDatabaseHas('pix_transfers', [
            'bank_account_id' => $this->bankAccount->id,
            'status' => PixTransferStatusEnum::PENDING->value,
            'pix_type' => PixTypeEnum::MANUAL->value,
        ]);
    }

    public function test_can_process_pix_transfer()
    {
        $this->actingAs($this->user);

        $pixTransfer = PixTransfer::create([
            'bank_account_id' => $this->bankAccount->id,
            'request_control_key' => 'test-control-key',
            'status' => PixTransferStatusEnum::PENDING,
            'pix_type' => PixTypeEnum::MANUAL,
            'recipient_data' => [
                'name' => 'João da Silva',
                'document_number' => '********901',
            ],
        ]);

        $payload = [
            'amount' => 100.50,
            'pix_message' => 'Pagamento teste',
            'latitude' => -23.5505,
            'longitude' => -46.6333,
            'password' => 'password123',
            'tfa_type' => 'device',
        ];

        $response = $this->postJson("/api/bank-account/transfers/pix/{$pixTransfer->request_control_key}", $payload);

        $response->assertStatus(200);

        $pixTransfer->refresh();
        $this->assertEquals(100.50, $pixTransfer->amount);
        $this->assertEquals('Pagamento teste', $pixTransfer->pix_message);
        $this->assertEquals(TfaTypeEnum::DEVICE, $pixTransfer->tfa_type);
    }

    public function test_can_confirm_pix_transfer()
    {
        $this->actingAs($this->user);

        $pixTransfer = PixTransfer::create([
            'bank_account_id' => $this->bankAccount->id,
            'request_control_key' => 'test-control-key',
            'status' => PixTransferStatusEnum::PENDING,
            'pix_type' => PixTypeEnum::MANUAL,
            'recipient_data' => [
                'name' => 'João da Silva',
                'document_number' => '********901',
            ],
            'amount' => 100.50,
            'password_hash' => Hash::make('password123'),
            'tfa_type' => TfaTypeEnum::DEVICE,
        ]);

        $response = $this->postJson("/api/bank-account/transfers/pix/{$pixTransfer->request_control_key}/confirm");

        $response->assertStatus(200);

        $pixTransfer->refresh();
        $this->assertEquals(PixTransferStatusEnum::COMPLETED, $pixTransfer->status);
        $this->assertNotNull($pixTransfer->confirmed_at);
        $this->assertNotNull($pixTransfer->completed_at);
    }

    public function test_can_get_pix_transfer_status()
    {
        $this->actingAs($this->user);

        $pixTransfer = PixTransfer::create([
            'bank_account_id' => $this->bankAccount->id,
            'request_control_key' => 'test-control-key',
            'status' => PixTransferStatusEnum::COMPLETED,
            'pix_type' => PixTypeEnum::MANUAL,
            'recipient_data' => [
                'name' => 'João da Silva',
                'document_number' => '********901',
            ],
            'amount' => 100.50,
            'completed_at' => now(),
        ]);

        $response = $this->getJson("/api/bank-account/transfers/pix/{$pixTransfer->request_control_key}/status");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'recipient' => [
                    'name',
                    'amount',
                    'date',
                ],
                'message',
            ]);
    }

    public function test_validates_required_fields_for_manual_pix()
    {
        $this->actingAs($this->user);

        $response = $this->postJson('/api/bank-account/transfers/pix/manual', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'ispb',
                'account_branch',
                'account_number',
                'account_digit',
                'account_type',
                'name',
                'document_number',
            ]);
    }

    public function test_validates_invalid_document_number()
    {
        $this->actingAs($this->user);

        $payload = [
            'ispb' => '********',
            'account_branch' => '1234',
            'account_number' => '********9',
            'account_digit' => '0',
            'account_type' => 'checking_account',
            'name' => 'João da Silva',
            'document_number' => '123', // Invalid document
        ];

        $response = $this->postJson('/api/bank-account/transfers/pix/manual', $payload);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['document_number']);
    }

    public function test_returns_404_for_nonexistent_transfer()
    {
        $this->actingAs($this->user);

        $response = $this->getJson('/api/bank-account/transfers/pix/nonexistent-key/status');

        $response->assertStatus(404)
            ->assertJson([
                'error' => 'Transferência PIX não encontrada.',
            ]);
    }
}
