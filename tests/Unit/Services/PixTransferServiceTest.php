<?php

namespace Tests\Unit\Services;

use App\Enums\BankAccount\PixTransferStatusEnum;
use App\Enums\BankAccount\PixTypeEnum;
use App\Enums\BankAccount\StatusEnum;
use App\Enums\BankAccount\TfaTypeEnum;
use App\Models\BankAccount;
use App\Models\PixTransfer;
use App\Models\UserApp;
use App\Services\BankAccount\PixTransferService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

class PixTransferServiceTest extends TestCase
{
    use RefreshDatabase;

    private PixTransferService $service;
    private UserApp $user;
    private BankAccount $bankAccount;

    protected function setUp(): void
    {
        parent::setUp();

        $this->service = new PixTransferService();
        
        $this->user = UserApp::factory()->create([
            'password' => Hash::make('password123'),
        ]);

        $this->bankAccount = BankAccount::factory()
            ->user($this->user)
            ->create([
                'status' => StatusEnum::ACCOUNT_CONFIRMED,
                'is_active' => true,
            ]);
    }

    public function test_can_create_manual_pix_transfer()
    {
        $recipientData = [
            'ispb' => '********',
            'account_branch' => '1234',
            'account_number' => '********9',
            'account_digit' => '0',
            'account_type' => 'checking_account',
            'name' => 'João da Silva',
            'document_number' => '***********',
        ];

        $pixTransfer = $this->service->createManualPixTransfer($this->bankAccount, $recipientData);

        $this->assertInstanceOf(PixTransfer::class, $pixTransfer);
        $this->assertEquals($this->bankAccount->id, $pixTransfer->bank_account_id);
        $this->assertEquals(PixTransferStatusEnum::PENDING, $pixTransfer->status);
        $this->assertEquals(PixTypeEnum::MANUAL, $pixTransfer->pix_type);
        $this->assertEquals($recipientData, $pixTransfer->recipient_data);
        $this->assertEquals($pixTransfer->pub_id, $pixTransfer->request_control_key);
    }

    public function test_can_process_pix_transfer()
    {
        $pixTransfer = PixTransfer::create([
            'bank_account_id' => $this->bankAccount->id,
            'request_control_key' => 'test-key',
            'status' => PixTransferStatusEnum::PENDING,
            'pix_type' => PixTypeEnum::MANUAL,
            'recipient_data' => ['name' => 'Test'],
        ]);

        $this->service->processPixTransfer(
            $pixTransfer,
            100.50,
            'Test message',
            -23.5505,
            -46.6333,
            'password123',
            TfaTypeEnum::DEVICE
        );

        $pixTransfer->refresh();

        $this->assertEquals(100.50, $pixTransfer->amount);
        $this->assertEquals('Test message', $pixTransfer->pix_message);
        $this->assertEquals(-23.5505, $pixTransfer->latitude);
        $this->assertEquals(-46.6333, $pixTransfer->longitude);
        $this->assertEquals(TfaTypeEnum::DEVICE, $pixTransfer->tfa_type);
        $this->assertTrue(Hash::check('password123', $pixTransfer->password_hash));
    }

    public function test_can_confirm_pix_transfer()
    {
        $pixTransfer = PixTransfer::create([
            'bank_account_id' => $this->bankAccount->id,
            'request_control_key' => 'test-key',
            'status' => PixTransferStatusEnum::PENDING,
            'pix_type' => PixTypeEnum::MANUAL,
            'recipient_data' => ['name' => 'Test'],
            'amount' => 100.50,
            'password_hash' => Hash::make('password123'),
            'tfa_type' => TfaTypeEnum::DEVICE,
        ]);

        $this->service->confirmPixTransfer($pixTransfer);

        $pixTransfer->refresh();

        $this->assertEquals(PixTransferStatusEnum::COMPLETED, $pixTransfer->status);
        $this->assertNotNull($pixTransfer->confirmed_at);
        $this->assertNotNull($pixTransfer->completed_at);
    }

    public function test_can_find_by_request_control_key()
    {
        $pixTransfer = PixTransfer::create([
            'bank_account_id' => $this->bankAccount->id,
            'request_control_key' => 'unique-test-key',
            'status' => PixTransferStatusEnum::PENDING,
            'pix_type' => PixTypeEnum::MANUAL,
            'recipient_data' => ['name' => 'Test'],
        ]);

        $found = $this->service->findByRequestControlKey('unique-test-key');

        $this->assertInstanceOf(PixTransfer::class, $found);
        $this->assertEquals($pixTransfer->id, $found->id);
    }

    public function test_returns_null_for_nonexistent_request_control_key()
    {
        $found = $this->service->findByRequestControlKey('nonexistent-key');

        $this->assertNull($found);
    }

    public function test_can_get_transfer_status()
    {
        $pixTransfer = PixTransfer::create([
            'bank_account_id' => $this->bankAccount->id,
            'request_control_key' => 'test-key',
            'status' => PixTransferStatusEnum::COMPLETED,
            'pix_type' => PixTypeEnum::MANUAL,
            'recipient_data' => ['name' => 'João da Silva'],
            'amount' => 100.50,
            'completed_at' => now(),
        ]);

        $status = $this->service->getTransferStatus($pixTransfer);

        $this->assertIsArray($status);
        $this->assertEquals('completed', $status['status']);
        $this->assertEquals('João da Silva', $status['recipient']['name']);
        $this->assertEquals(100.50, $status['recipient']['amount']);
        $this->assertNotNull($status['recipient']['date']);
        $this->assertEquals('', $status['message']);
    }

    public function test_throws_exception_when_processing_invalid_transfer()
    {
        $pixTransfer = PixTransfer::create([
            'bank_account_id' => $this->bankAccount->id,
            'request_control_key' => 'test-key',
            'status' => PixTransferStatusEnum::COMPLETED, // Already completed
            'pix_type' => PixTypeEnum::MANUAL,
            'recipient_data' => ['name' => 'Test'],
        ]);

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('PIX transfer cannot be processed in current state');

        $this->service->processPixTransfer(
            $pixTransfer,
            100.50,
            'Test message',
            -23.5505,
            -46.6333,
            'password123',
            TfaTypeEnum::DEVICE
        );
    }

    public function test_throws_exception_when_confirming_invalid_transfer()
    {
        $pixTransfer = PixTransfer::create([
            'bank_account_id' => $this->bankAccount->id,
            'request_control_key' => 'test-key',
            'status' => PixTransferStatusEnum::COMPLETED, // Already completed
            'pix_type' => PixTypeEnum::MANUAL,
            'recipient_data' => ['name' => 'Test'],
        ]);

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('PIX transfer cannot be confirmed in current state');

        $this->service->confirmPixTransfer($pixTransfer);
    }
}
