<?php

use App\Http\Controllers\Api\WoxAI\WoxAiController;
use App\Http\Controllers\App\AgendamentoController;
use App\Http\Controllers\App\AtendimentoController;
use App\Http\Controllers\App\Auth\AuthController;
use App\Http\Controllers\App\Auth\ResetSenhaController;
use App\Http\Controllers\App\BankAccount\BankAccountController;
use App\Http\Controllers\App\BolsaoLeadController;
use App\Http\Controllers\App\Cadastro\CamisaTamanhosController;
use App\Http\Controllers\App\Cadastro\CanaisController;
use App\Http\Controllers\App\Cadastro\EnderecoController;
use App\Http\Controllers\App\Cadastro\GerentesController;
use App\Http\Controllers\App\Cadastro\ImobiliariasController;
use App\Http\Controllers\App\Cadastro\RegionaisController;
use App\Http\Controllers\App\Cadastro\RegisterController;
use App\Http\Controllers\App\Cadastro\UploadDocumentoController;
use App\Http\Controllers\App\Cadastro\VerificarDadosController;
use App\Http\Controllers\App\ChecagemController;
use App\Http\Controllers\App\CheckAppController;
use App\Http\Controllers\App\Checkin\CheckinController;
use App\Http\Controllers\App\Checkin\CheckoutController;
use App\Http\Controllers\App\Checkin\VerificarController;
use App\Http\Controllers\App\ConfirmacaoController;
use App\Http\Controllers\App\FilaController;
use App\Http\Controllers\App\LeadsController;
use App\Http\Controllers\App\NotificacoesController;
use App\Http\Controllers\App\Plantao\ListaController;
use App\Http\Controllers\App\Rh\CadastroController;
use App\Http\Controllers\App\Rh\CadastroDadosPessoaisController;
use App\Http\Controllers\App\Rh\CadastroDocumentosController;
use App\Http\Controllers\App\Rh\CadastroEnderecoController;
use App\Http\Controllers\App\Rh\CadastroFotoController;
use App\Http\Controllers\App\Rh\CadastroSenhaController;
use App\Http\Controllers\App\UserController;
use App\Http\Controllers\Plantao\ClientesController;
use App\Http\Controllers\Plantao\PlantaoController;
use App\Http\Controllers\Service\Corretores\UploadArquivosController;
use App\Http\Controllers\Service\HomeApp\AppController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/
Route::post('IAContentCampanha', [WoxAiController::class, 'IAContentCampanha'])->name('IAContentCampanha');
Route::post('IAContentTexto', [WoxAiController::class, 'IAContentTexto'])->name('IAContentTexto');
Route::post('IATiraDuvidas', [WoxAiController::class, 'IATiraDuvidas'])->name('IATiraDuvidas');

Route::prefix('auth')->name('auth.')->middleware(['auth'])->group(function () {
    Route::post('login', [AuthController::class, 'login'])->name('login')->withoutMiddleware(['auth']);
    Route::post('logout', [AuthController::class, 'logout'])->name('logout');
    Route::post('refresh', [AuthController::class, 'refresh'])->name('refresh');
    Route::post('reset-senha', ResetSenhaController::class)->name('reset-senha')->withoutMiddleware(['auth']);
});

Route::get('check-app', CheckAppController::class)->name('check-app');

Route::prefix('cadastro')->name('cadastro.')->group(function () {
    Route::post('/', [RegisterController::class, 'store'])->name('store');
    Route::post('upload', [UploadDocumentoController::class, 'upload'])->name('upload');
    Route::post('upload-arquivos', [UploadArquivosController::class, 'upload'])->name('upload-arquivos');
    Route::get('regionais', RegionaisController::class)->name('regionais');
    Route::get('gerentes', GerentesController::class)->name('gerentes');
    Route::get('camisa-tamanhos', CamisaTamanhosController::class)->name('camisa-tamanhos');
    Route::get('canais', CanaisController::class)->name('canais');
    Route::get('imobiliarias', ImobiliariasController::class)->name('imobiliarias');
    Route::post('check-cpf', [VerificarDadosController::class, 'checkCpf'])->name('check-cpf');
    Route::post('check-email', [VerificarDadosController::class, 'checkEmail'])->name('check-email');
    Route::post('check-apelido', [VerificarDadosController::class, 'checkApelido'])->name('check-apelido');
    Route::post('check-creci', [VerificarDadosController::class, 'checkCreci'])->name('check-creci');
    Route::post('endereco', EnderecoController::class)->name('endereco');
    Route::get('sugestao-apelidos', [VerificarDadosController::class, 'getSugestaoApelidos'])->name('sugestao-apelidos');
    Route::get('check/{user}', [RegisterController::class, 'checkCadastro'])->name('check');
});

Route::prefix('confirmacao')->name('confirmacao.')->group(function () {
    Route::get('/agendamentos/{id}', [ConfirmacaoController::class, 'show'])->name('agendamentos.show');
    Route::post('/agendamentos/{id}', [ConfirmacaoController::class, 'update'])->name('agendamentos.update');
});

Route::middleware(['auth'])->group(function () {
    Route::get('/user', [UserController::class, 'me'])->name('user');
    Route::get('/home', [UserController::class, 'home'])->name('user-home');

    Route::get('/icon-preview/{iconDisk}/{path}', [AppController::class, 'iconPreview'])->name('icon-preview');

    Route::delete('/user', [UserController::class, 'delete'])->name('delete');

    Route::middleware(['check-corretor-aprovado'])->group(function () {

        Route::prefix('crm')->name('crm.')->group(function () {
            Route::get('/plantoes', [PlantaoController::class, 'list'])->name('plantoes.list');
            Route::get('/clientes', [ClientesController::class, 'list'])->name('clientes.list');
            Route::get('/clientes/{id}', [ClientesController::class, 'show'])->name('clientes.show');
            Route::get('/clientes/{id}/edit', [ClientesController::class, 'showEdit'])->name('clientes.showEdit');
            Route::put('/clientes/{id}/edit', [ClientesController::class, 'updateEdit'])->name('clientes.updateEdit');
            Route::post('/clientes/{plantao}', [ClientesController::class, 'store'])->name('clientes.store');
            Route::post('/ativar-cliente/{corretorClienteId}/{tipo?}', [ClientesController::class, 'ativarCliente'])->name('ativarCliente');
            Route::get('/agendamentos', [AgendamentoController::class, 'index'])->name('agendamentos.index');
            Route::get('/agendamentos/{id}', [AgendamentoController::class, 'show'])->name('agendamentos.show');
            Route::put('/agendamentos/{id}', [AgendamentoController::class, 'update'])->name('agendamentos.update');
            Route::post('/agendamentos', [AgendamentoController::class, 'store'])->name('agendamentos.store');
            Route::delete('/agendamentos/{id}', [AgendamentoController::class, 'delete'])->name('agendamentos.delete');

            Route::group([], base_path('routes/api.contas.php'));
        });

        Route::post('checkin', CheckinController::class)->name('checkin');
        Route::post('checkin/verificar', VerificarController::class)->name('checkin.verificar');
        Route::post('checkout', CheckoutController::class)->name('checkout');

        Route::get('fila', [FilaController::class, 'index'])->name('fila');
        Route::get('fila/lista', [FilaController::class, 'lista'])->name('fila.lista');

        Route::get('plantao', ListaController::class)->name('plantao');

        Route::get('plantoes/{id}/contato', [PlantaoController::class, 'gpCoordenadoresLista'])->name('plantoes.contatos');

        Route::prefix('atendimento')->name('atendimento.')->group(function () {
            Route::post('iniciar', [AtendimentoController::class, 'iniciar'])->name('iniciar');
            Route::post('encerrar', [AtendimentoController::class, 'encerrar'])->name('encerrar');
        });

        Route::group([], base_path('routes/api.pagadoria.php'));
        Route::group([], base_path('routes/api.treinamentos.php'));
        Route::group([], base_path('routes/api.arquivos.php'));
        Route::group([], base_path('routes/api.repasses.php'));
        Route::group([], base_path('routes/api.repasses-salesforce.php'));
        Route::group([], base_path('routes/api.bank-account.php'));
    });

    Route::prefix('notificacoes')->name('notificacoes.')->group(function () {
        Route::get('/', [NotificacoesController::class, 'index'])->name('index');
        Route::get('/{pubId}', [NotificacoesController::class, 'show'])->name('show');
        Route::delete('/{pubId}', [NotificacoesController::class, 'destroy']);
    });

    Route::prefix('rh')->name('rh.')->group(function () {
        Route::get('/', [CadastroController::class, 'statusCadastro'])->name('status-cadastro');
        Route::get('documento/{tipo}/preview', [CadastroController::class, 'getDocPreview'])->name('doc-preview');

        Route::prefix('cadastro')->name('cadastro.')->group(function () {
            Route::post('/', [CadastroController::class, 'sendToAprovacao'])->name('send-aprovacao');
            Route::post('check-apelido', [VerificarDadosController::class, 'checkApelido'])->name('check-apelido');
            Route::post('check-email', [VerificarDadosController::class, 'checkEmail'])->name('check-email');

            Route::get('dados-pessoais', [CadastroDadosPessoaisController::class, 'show'])->name('dados-pessoais');
            Route::put('dados-pessoais', [CadastroDadosPessoaisController::class, 'update']);
            Route::get('endereco', [CadastroEnderecoController::class, 'show'])->name('endereco');
            Route::put('endereco', [CadastroEnderecoController::class, 'update']);
            Route::get('documentos', [CadastroDocumentosController::class, 'show'])->name('documentos');
            Route::put('documentos', [CadastroDocumentosController::class, 'update']);
            Route::get('foto', [CadastroFotoController::class, 'show'])->name('foto');
            Route::put('foto', [CadastroFotoController::class, 'update']);

            Route::put('senha', [CadastroSenhaController::class, 'update']);
        });
    });

    Route::prefix('leads')->name('leads.')->group(function () {
        Route::get('/', [LeadsController::class, 'index'])->name('index');
        Route::get('{leadDisparo}', [LeadsController::class, 'show'])->name('show');
    });

    Route::prefix('bolsao')->name('bolsao.')
        ->middleware(['check-bolsao-access'])->group(function () {
            Route::get('/', [BolsaoLeadController::class, 'index'])->name('index');
            Route::post('/ativar/{lead:pub_id}', [BolsaoLeadController::class, 'ativar'])->name('ativar');
        });

    Route::prefix('checagem')->name('checagem.')->group(function () {
        Route::post('/confirmar', [ChecagemController::class, 'confirmar'])->name('confirmar');
    });
});
