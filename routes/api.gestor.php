<?php

use App\Http\Controllers\Api\WoxAI\WoxAiController;
use App\Http\Controllers\App\AgendamentoController;
use App\Http\Controllers\Gestor\Auth\AuthController;
use App\Http\Controllers\Gestor\Auth\ResetSenhaController;
use App\Http\Controllers\Gestor\Auth\SolicitacaoCadastroController;
use App\Http\Controllers\Gestor\CheckAppController;
use App\Http\Controllers\Gestor\ClienteController;
use App\Http\Controllers\Gestor\CorretoresController;
use App\Http\Controllers\Gestor\DiretoresController;
use App\Http\Controllers\Gestor\FiltroPlantoesController;
use App\Http\Controllers\Gestor\FiltroRegionaisController;
use App\Http\Controllers\Gestor\GerentesController;
use App\Http\Controllers\Gestor\Leads\CorretoresController as DashLeadsCorretoresController;
use App\Http\Controllers\Gestor\Leads\DiretoresController as DashLeadsDiretoresController;
use App\Http\Controllers\Gestor\Leads\EvolucaoController;
use App\Http\Controllers\Gestor\Leads\GerentesController as DashLeadsGerentesController;
use App\Http\Controllers\Gestor\Leads\HistoricoController;
use App\Http\Controllers\Gestor\Leads\RankingController;
use App\Http\Controllers\Gestor\Leads\SuperintendentesController as DashLeadsSuperintendentesController;
use App\Http\Controllers\Gestor\LeadsController;
use App\Http\Controllers\Gestor\NotificacoesController;
use App\Http\Controllers\Gestor\Plantoes\ProdutosPlantoesController;
use App\Http\Controllers\Gestor\PlantoesController;
use App\Http\Controllers\Gestor\Rh\CadastroSenhaController;
use App\Http\Controllers\Gestor\SuperintendentesController;
use App\Http\Controllers\Gestor\UserController;
use App\Http\Controllers\Gestor\VendasController;
use App\Http\Controllers\Plantao\PlantaoController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/
Route::post('IAContentCampanha', [WoxAiController::class, 'IAContentCampanha'])->name('IAContentCampanha');
Route::post('IAContentTexto', [WoxAiController::class, 'IAContentTexto'])->name('IAContentTexto');
Route::post('IATiraDuvidas', [WoxAiController::class, 'IATiraDuvidas'])->name('IATiraDuvidas');

Route::post('/cadastro', [SolicitacaoCadastroController::class, 'send']);

Route::prefix('auth')->name('auth.')->middleware(['auth:gestor'])->group(function () {
    Route::post('login', [AuthController::class, 'login'])->name('login')->withoutMiddleware(['auth:gestor']);
    Route::post('logout', [AuthController::class, 'logout'])->name('logout');
    Route::post('refresh', [AuthController::class, 'refresh'])->name('refresh');
    Route::post('reset-senha', ResetSenhaController::class)->name('reset-senha')->withoutMiddleware(['auth:gestor']);
});

Route::get('check-app', CheckAppController::class)->name('check-app');

Route::prefix('agendamentos')->middleware(['auth:gestor'])->name('agendamentos.')->group(function () {
    Route::middleware(['set-regional-id-gestor', 'set-dates-gestor', 'set-plantoes-ids-gestor'])->group(function () {
        Route::middleware(['perfil-gestor'])->group(function () {
            Route::get('proximos', [AgendamentoController::class, 'proximosAgendamentos'])->name('proximos');
            Route::get('total', [AgendamentoController::class, 'total'])->name('total');
            Route::get('evolucao', [AgendamentoController::class, 'evolucao'])->name('evolucao');
            Route::get('ranking-concretizados', [AgendamentoController::class, 'rankingConcretizados'])->name('ranking');
            Route::get('ranking-nao-concretizados', [AgendamentoController::class, 'rankingNaoConcretizados'])->name('ranking.nao-concretizados');

            Route::get('total-corretores', [AgendamentoController::class, 'totalCorretores'])->name('total.corretores');
        });

        Route::get('total-gerentes', [AgendamentoController::class, 'totalgerentes'])
            ->middleware(['perfil-gestor:master,gp,coordenador,diretor,superintendente'])
            ->name('total.gerentes');

        Route::get('total-superintendentes', [AgendamentoController::class, 'totalSuperintendentes'])
            ->middleware(['perfil-gestor:master,gp,coordenador,diretor'])
            ->name('total.superintendentes');

        // TODO
        //            Route::get('total-diretores', [AgendamentoController::class, 'totalDiretores'])
        //                ->middleware(['perfil-gestor:master,gp,coordenador'])
        //                ->name('total.diretores');
    });
});

Route::get('/plantoes', [PlantaoController::class, 'list'])->name('plantoes.list');
Route::get('/agendamentos', [AgendamentoController::class, 'agendamentos'])->name('agendamentos.consult');
Route::get('/agendamentos/diretores', [AgendamentoController::class, 'diretores'])->name('agendamentos.diretores');
Route::get('/agendamentos/superintendentes', [AgendamentoController::class, 'superintendentes'])->name('agendamentos.superintendentes');
Route::get('/agendamentos/gerentes', [AgendamentoController::class, 'gerentes'])->name('agendamentos.gerentes');
Route::get('/agendamentos/corretores', [AgendamentoController::class, 'corretores'])->name('agendamentos.corretores');
Route::get('/agendamentos/{id}', [AgendamentoController::class, 'consultAppointmentGestor'])->name('agendamentos.show');

Route::get('/list-regionais', [FiltroRegionaisController::class, 'index'])
    ->name('list-regionais');

Route::middleware(['auth:gestor'])->group(function () {

    Route::middleware(['perfil-gestor', 'set-regional-id-gestor'])->group(function () {
        Route::get('/user/app', [UserController::class, 'app'])->name('user.app');
        Route::get('/user/dashboard', [UserController::class, 'dashboard'])->name('user.dashboard');
        Route::delete('/user', [UserController::class, 'delete'])->name('user.index');
    });

    Route::get('/clientes', [ClienteController::class, 'index'])->name('gestor.clientes');

//    Route::get('/list-regionais', [FiltroRegionaisController::class, 'index'])
//        ->middleware(['perfil-gestor:master'])
//        ->name('list-regionais');

    Route::middleware(['perfil-gestor', 'set-regional-id-gestor', 'set-plantoes-ids-gestor'])->group(function () {
        Route::get('/list-plantoes', [FiltroPlantoesController::class, 'index'])->name('list-plantoes');

        Route::put('plantao-remover-substituto', [PlantoesController::class, 'removerSubstituto'])->name('plantao.remover.substituto');
        Route::post('plantao-adicionar-substituto', [PlantoesController::class, 'addSubstituto'])->name('plantao.adicionar.substituto');
        Route::get('plantoes/{plantao_id}/list-users', [PlantoesController::class, 'listarUsersRegional'])->name('plantoes.list_users');

        Route::prefix('/vendas')->name('vendas.')->group(function () {

            Route::get('/', [VendasController::class, 'index'])
                ->middleware('set-dates-gestor')
                ->name('index');

            Route::get('resumo', [VendasController::class, 'resumo'])->name('resumo');
            Route::get('/total', [VendasController::class, 'total'])
                ->middleware('set-dates-gestor')
                ->name('total');

            Route::get('ranking', [VendasController::class, 'ranking'])
                ->middleware(['set-dates-gestor'])
                ->name('ranking');
        });

        Route::get('/corretores', [CorretoresController::class, 'index'])
            ->middleware(['set-dates-gestor'])
            ->name('corretores.index');

        Route::middleware(['set-dates-gestor'])->prefix('/corretores')->name('corretores.')->group(function () {
            Route::get('download-extrato', [CorretoresController::class, 'downloadExtrato'])->name('download-extrato');
            Route::get('{gestorCorretor}', [CorretoresController::class, 'interna'])->name('interna');
            Route::get('{gestorCorretor}/checkins', [CorretoresController::class, 'checkins'])->name('checkins');
            Route::get('{gestorCorretor}/vendas', [CorretoresController::class, 'vendas'])->name('vendas');
            Route::get('{gestorCorretor}/atendimentos', [CorretoresController::class, 'atendimentos'])->name('atendimentos');
            Route::get('{gestorCorretor}/plantoes', [CorretoresController::class, 'plantoesVisitados'])->name('plantoes');
        });
    });

    Route::middleware(['perfil-gestor:master,gp,coordenador,diretor,superintendente', 'set-regional-id-gestor', 'set-dates-gestor'])->group(function () {
        Route::get('/gerentes', [GerentesController::class, 'index'])->name('gerentes.index');

        Route::middleware(['set-plantoes-ids-gestor'])->prefix('/gerentes')->name('gerentes.')->group(function () {
            Route::get('download-extrato', [GerentesController::class, 'downloadExtrato'])->name('download-extrato');
            Route::get('{gestorCorretor}', [GerentesController::class, 'interna'])->name('interna');
            Route::get('{gestorCorretor}/checkins', [GerentesController::class, 'checkins'])->name('checkins');
            Route::get('{gestorCorretor}/checkins-grupo', [GerentesController::class, 'checkinsGrupo'])->name('checkinsGrupo');
            Route::get('{gestorCorretor}/vendas', [GerentesController::class, 'vendas'])->name('vendas');
            Route::get('{gestorCorretor}/atendimentos', [GerentesController::class, 'atendimentos'])->name('atendimentos');
            Route::get('{gestorCorretor}/plantoes', [GerentesController::class, 'plantoesVisitados'])->name('plantoes');
        });
    });

    Route::middleware(['perfil-gestor:master,gp,coordenador,diretor', 'set-regional-id-gestor', 'set-dates-gestor'])->group(function () {
        Route::get('/superintendentes', [SuperintendentesController::class, 'index'])->name('superintendentes.index');

        Route::middleware(['set-plantoes-ids-gestor'])->prefix('/superintendentes')->name('superintendentes.')->group(function () {
            Route::get('download-extrato', [SuperintendentesController::class, 'downloadExtrato'])->name('download-extrato');
            Route::get('{gestorCorretor}', [SuperintendentesController::class, 'interna'])->name('interna');
            Route::get('{gestorCorretor}/checkins', [SuperintendentesController::class, 'checkins'])->name('checkins');
            Route::get('{gestorCorretor}/checkins-grupo', [SuperintendentesController::class, 'checkinsGrupo'])->name('checkinsGrupo');
            Route::get('{gestorCorretor}/vendas', [SuperintendentesController::class, 'vendas'])->name('vendas');
            Route::get('{gestorCorretor}/atendimentos', [SuperintendentesController::class, 'atendimentos'])->name('atendimentos');
            Route::get('{gestorCorretor}/plantoes', [SuperintendentesController::class, 'plantoesVisitados'])->name('plantoes');
        });
    });

    Route::middleware(['perfil-gestor:master,gp,coordenador', 'set-regional-id-gestor', 'set-dates-gestor'])->group(function () {
        Route::get('/diretores', [DiretoresController::class, 'index'])->name('diretores.index');

        Route::middleware(['set-plantoes-ids-gestor'])->prefix('/diretores')->name('diretores.')->group(function () {
            Route::get('download-extrato', [DiretoresController::class, 'downloadExtrato'])->name('download-extrato');
            Route::get('{gestorCorretor}', [DiretoresController::class, 'interna'])->name('interna');
            Route::get('{gestorCorretor}/checkins', [DiretoresController::class, 'checkins'])->name('checkins');
            Route::get('{gestorCorretor}/checkins-grupo', [DiretoresController::class, 'checkinsGrupo'])->name('checkinsGrupo');
            Route::get('{gestorCorretor}/vendas', [DiretoresController::class, 'vendas'])->name('vendas');
            Route::get('{gestorCorretor}/atendimentos', [DiretoresController::class, 'atendimentos'])->name('atendimentos');
            Route::get('{gestorCorretor}/plantoes', [DiretoresController::class, 'plantoesVisitados'])->name('plantoes');
        });
    });

    Route::middleware(['perfil-gestor', 'set-regional-id-gestor', 'set-plantoes-ids-gestor'])->prefix('plantoes')->name('plantoes.')->group(function () {
        Route::get('resumo', [PlantoesController::class, 'resumo'])->name('resumo');
        Route::get('plantoes-ativos', [PlantoesController::class, 'plantoesAtivos'])->name('ativos');
        Route::get('corretores-ativos', [PlantoesController::class, 'plantoesCorretoresAtivos'])->name('corretores-ativos');
        Route::get('clientes-atendidos', [PlantoesController::class, 'plantoesClientesAtendidos'])->name('clientes-atendidos');
        Route::get('clientes-em-atendimento', [PlantoesController::class, 'plantoesClientesEmAtendimento'])->name('clientes-em-atendimento');

        Route::get('produtos/resumo-produtos', [PlantoesController::class, 'produtosResumo'])->name('produtos.resumo-produtos');

        Route::prefix('produtos/resumo-plantoes')->middleware(['set-dates-gestor'])->name('produtos.resumo-plantoes.')->group(function () {
            Route::get('/', [PlantoesController::class, 'produtosPlantoes'])->name('full');
            Route::get('total', [ProdutosPlantoesController::class, 'total'])->name('total');
            Route::get('corretores', [ProdutosPlantoesController::class, 'corretores'])->name('corretores');
            Route::get('gerentes', [ProdutosPlantoesController::class, 'gerentes'])->name('gerentes');
            Route::get('superintendentes', [ProdutosPlantoesController::class, 'superintendentes'])->name('superintendentes');
            Route::get('diretores', [ProdutosPlantoesController::class, 'diretores'])->name('diretores');
            Route::get('imobiliarias', [ProdutosPlantoesController::class, 'imobiliarias'])->name('imobiliarias');
        });
    });

    Route::middleware(['perfil-gestor', 'set-regional-id-gestor', 'set-dates-gestor'])->prefix('plantoes')->name('plantoes.')->group(function () {
        Route::get('/', [PlantoesController::class, 'list'])->name('list');
        Route::get('{plantao}', [PlantoesController::class, 'interna'])->name('interna');
        Route::get('{plantao}/corretores', [PlantoesController::class, 'corretores'])->name('corretores');
        Route::get('{plantao}/gerentes', [PlantoesController::class, 'gerentes'])->name('gerentes');
        Route::get('{plantao}/superintendentes', [PlantoesController::class, 'superintendentes'])->name('superintendentes');
        Route::get('{plantao}/diretores', [PlantoesController::class, 'diretores'])->name('diretores');
    });

    Route::get('plantoes/{id}/contato', [PlantoesController::class, 'gpCoordenadoresLista'])->name('plantoes.contatos');

    Route::middleware(['perfil-gestor', 'set-regional-id-gestor'])->prefix('notificacoes')->name('notificacoes.')->group(function () {
        Route::get('/', [NotificacoesController::class, 'index'])->name('index');
        Route::get('criar', [NotificacoesController::class, 'criar'])->name('criar');
        Route::post('criar', [NotificacoesController::class, 'enviar']);
        Route::get('buscar-usuarios', [NotificacoesController::class, 'buscarUsuarios'])->name('buscar-usuarios');
        Route::get('/{pubId}', [NotificacoesController::class, 'show'])->name('show');
        Route::delete('/{pubId}', [NotificacoesController::class, 'destroy']);
    });

    Route::middleware(['perfil-gestor'])->group(function () {
        Route::prefix('rh')->name('rh.')->group(function () {
            Route::prefix('cadastro')->name('cadastro.')->group(function () {
                Route::put('senha', [CadastroSenhaController::class, 'update']);
            });
        });

        Route::group([], base_path('routes/api.pagadoria.php'));
        Route::group([], base_path('routes/api.repasses.php'));
        Route::group([], base_path('routes/api.repasses-salesforce.php'));
        Route::group([], base_path('routes/api.treinamentos.php'));
        Route::group([], base_path('routes/api.arquivos.php'));
        Route::group([], base_path('routes/api.bank-account.php'));
    });

    Route::prefix('leads')->name('leads.')->group(function () {

        Route::middleware(['set-regional-id-gestor', 'set-dates-gestor', 'set-plantoes-ids-gestor'])->group(function () {
            Route::middleware(['perfil-gestor'])->group(function () {
                Route::get('historico', HistoricoController::class)->name('historico');
                Route::get('evolucao', EvolucaoController::class)->name('evolucao');
                Route::get('ranking-ativacoes', [RankingController::class, 'ativados'])->name('ranking-ativacoes');
                Route::get('ranking-expiracoes', [RankingController::class, 'expirados'])->name('ranking-expiracoes');

                Route::get('lista/corretores', DashLeadsCorretoresController::class)->name('lista-corretores');
            });

            Route::get('lista/gerentes', DashLeadsGerentesController::class)
                ->middleware(['perfil-gestor:master,gp,coordenador,diretor,superintendente'])
                ->name('lista-gerentes');

            Route::get('lista/superintendentes', DashLeadsSuperintendentesController::class)
                ->middleware(['perfil-gestor:master,gp,coordenador,diretor'])
                ->name('lista-superintendentes');

            Route::get('lista/diretores', DashLeadsDiretoresController::class)
                ->middleware(['perfil-gestor:master,gp,coordenador'])
                ->name('lista-diretores');
        });

        Route::get('diretores', [LeadsController::class, 'diretores'])
            ->middleware(['perfil-gestor:master,gp,coordenador'])
            ->name('diretores');

        Route::get('superintendentes', [LeadsController::class, 'superintendentes'])
            ->middleware(['perfil-gestor:master,gp,coordenador,diretor'])
            ->name('superintendentes');

        Route::get('gerentes', [LeadsController::class, 'gerentes'])
            ->middleware(['perfil-gestor:master,gp,coordenador,diretor,superintendente'])
            ->name('gerentes');

        Route::middleware(['perfil-gestor'])->group(function () {
            Route::get('corretores', [LeadsController::class, 'corretores'])->name('corretores');
            Route::get('leads', [LeadsController::class, 'leads'])->name('leads');
            Route::get('{leadDisparo}', [LeadsController::class, 'show'])->name('show');
        });
    });
});
